<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload New Image - Real or AI?</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="admin-header">
            <h1>Upload New Image</h1>
            <button id="logout-btn" class="vote-btn ai-btn">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>
        
        <div class="admin-nav">
            <a href="index.html">Dashboard</a>
            <a href="upload.html" class="active">Upload New Image</a>
            <a href="schedule.html">Schedule</a>
        </div>
        
        <div class="admin-container">
            <form id="upload-form" class="admin-form">
                <div class="form-group">
                    <label for="image-file">Upload Image</label>
                    <input type="file" id="image-file" name="image" accept="image/*" required>
                    <p class="form-help">Recommended size: 1200x800px. Max file size: 5MB.</p>
                </div>
                
                <div class="image-preview-wrapper" style="display: none;">
                    <h3>Image Preview</h3>
                    <img id="image-preview" class="image-preview" src="#" alt="Image preview">
                </div>
                
                <div class="form-group">
                    <label for="correct-answer">Correct Answer</label>
                    <select id="correct-answer" name="correctAnswer" required>
                        <option value="">-- Select Answer --</option>
                        <option value="real">Real</option>
                        <option value="ai">AI-Generated</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="schedule-date">Schedule Date</label>
                    <input type="date" id="schedule-date" name="scheduleDate" required>
                    <p class="form-help">The image will be displayed on this date.</p>
                </div>
                
                <div class="form-group">
                    <label for="image-description">Image Description (Optional)</label>
                    <textarea id="image-description" name="description" rows="3" placeholder="Add a description or notes about this image"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="vote-btn real-btn">
                        <i class="fas fa-upload"></i> Upload and Schedule
                    </button>
                    <button type="button" id="cancel-btn" class="vote-btn ai-btn">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                </div>
            </form>
            
            <div id="upload-message" class="upload-message"></div>
        </div>
        
        <footer>
            <p>&copy; 2025 Real or AI Challenge - Admin Panel</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if admin is logged in
            const isLoggedIn = localStorage.getItem('adminLoggedIn') === 'true';
            
            if (!isLoggedIn) {
                // Redirect to login page if not logged in
                window.location.href = 'login.html';
                return;
            }
            
            // Logout button handler
            document.getElementById('logout-btn').addEventListener('click', function() {
                localStorage.removeItem('adminLoggedIn');
                window.location.href = 'login.html';
            });
            
            // Cancel button handler
            document.getElementById('cancel-btn').addEventListener('click', function() {
                window.location.href = 'index.html';
            });
            
            // Image preview functionality
            const imageFileInput = document.getElementById('image-file');
            const imagePreview = document.getElementById('image-preview');
            const imagePreviewWrapper = document.querySelector('.image-preview-wrapper');
            
            imageFileInput.addEventListener('change', function() {
                const file = this.files[0];
                
                if (file) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        imagePreviewWrapper.style.display = 'block';
                    };
                    
                    reader.readAsDataURL(file);
                } else {
                    imagePreviewWrapper.style.display = 'none';
                }
            });
            
            // Set minimum date to tomorrow
            const scheduleDateInput = document.getElementById('schedule-date');
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            
            // Format date as YYYY-MM-DD for the date input
            const formattedDate = tomorrow.toISOString().split('T')[0];
            scheduleDateInput.min = formattedDate;
            scheduleDateInput.value = formattedDate;
            
            // Form submission handler
            const uploadForm = document.getElementById('upload-form');
            const uploadMessage = document.getElementById('upload-message');
            
            uploadForm.addEventListener('submit', function(event) {
                event.preventDefault();
                
                // Get form data
                const formData = new FormData(this);
                const file = imageFileInput.files[0];
                const correctAnswer = document.getElementById('correct-answer').value;
                const scheduleDate = document.getElementById('schedule-date').value;
                const description = document.getElementById('image-description').value;
                
                // Validate form data
                if (!file) {
                    uploadMessage.textContent = 'Please select an image file.';
                    uploadMessage.className = 'upload-message error';
                    return;
                }
                
                if (!correctAnswer) {
                    uploadMessage.textContent = 'Please select the correct answer.';
                    uploadMessage.className = 'upload-message error';
                    return;
                }
                
                if (!scheduleDate) {
                    uploadMessage.textContent = 'Please select a schedule date.';
                    uploadMessage.className = 'upload-message error';
                    return;
                }
                
                // In a real app, this would send the data to a server
                console.log('Upload data:', {
                    file: file.name,
                    correctAnswer,
                    scheduleDate,
                    description
                });
                
                // Show success message
                uploadMessage.textContent = 'Image uploaded and scheduled successfully!';
                uploadMessage.className = 'upload-message success';
                
                // Reset form after 2 seconds and redirect to dashboard
                setTimeout(() => {
                    uploadForm.reset();
                    imagePreviewWrapper.style.display = 'none';
                    window.location.href = 'index.html';
                }, 2000);
            });
        });
    </script>
    
    <style>
        .form-help {
            font-size: 0.8rem;
            color: var(--gray-color);
            margin-top: 0.25rem;
        }
        
        .image-preview-wrapper {
            margin: 1.5rem 0;
        }
        
        .form-actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .upload-message {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: var(--border-radius);
            text-align: center;
        }
        
        .upload-message.success {
            background-color: rgba(46, 204, 113, 0.2);
            color: var(--secondary-color);
        }
        
        .upload-message.error {
            background-color: rgba(231, 76, 60, 0.2);
            color: var(--danger-color);
        }
        
        @media (max-width: 768px) {
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</body>
</html>
