module FindOrCreateCustomer
  extend self

  def initialize(user)
    @user = user
  end

  def call(user, fetch_payment_methods: false)
    Stripe.api_key = Rails.application.credentials.secret_key || ENV['STRIPE_SECRET_KEY']
    customer = nil
    customer_id = user&.stripe_customer_id

    if customer_id.blank?
      customer = Stripe::Customer.create(
        {
          email: user.email,
          description: "Stripe customer for Acquire - #{user.name}"
        },
        {
          idempotency_key: Time.now.to_i.to_s
        }
      )

      if customer.id
        user.update_attribute(:stripe_customer_id, customer.id)
        customer_id = customer.id
      end
    else
      customer = Stripe::Customer.retrieve(customer_id)
    end

    retry_count = 0
    payment_methods = nil

    loop do
      payment_methods = Stripe::PaymentMethod.list({ type: 'card', customer: customer_id })

      break if payment_methods.data.first.present? || retry_count > 2 || !fetch_payment_methods

      retry_count += 1
      sleep(1)
    end

    current_pm_id = user.stripe_customer_token
    pm_id = payment_methods&.data&.first

    if pm_id.present? && (current_pm_id.nil? || pm_id&.id != current_pm_id)
      user.update_attribute(:stripe_customer_token, pm_id.id)
    end

    if user.stripe_card_token.present? && !current_pm_id.present?
      card_token = user.stripe_card_token

      payment_method = Stripe::PaymentMethod.create({
        type: 'card',
        card: {
          token: card_token
        }
      })

      if payment_method.id
        user.update_attribute(:stripe_customer_token, payment_method.id)
        payment_method_id = payment_method.id

        attached_payment_method = Stripe::PaymentMethod.attach(
          payment_method_id,
          customer: customer_id
        )

        if attached_payment_method
          begin
            Stripe::Customer.update(
              customer_id,
              {
                invoice_settings: {
                  default_payment_method: attached_payment_method.id
                }
              }
            )

          rescue Stripe::StripeError => e
            puts "Stripe customer update error: #{e.message}"
            return  "Stripe customer payment method update error: #{e.message}"
          end
        else
          return "Unexpected error occured while attaching the payment method"
        end
      else
        return "Unexpected error occured while creating the payment method"
      end
    end

    customer
  end
end
