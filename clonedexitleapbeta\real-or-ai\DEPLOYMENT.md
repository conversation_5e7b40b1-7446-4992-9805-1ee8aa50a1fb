# Deploying "Real or AI?" to Vercel

This guide will walk you through the process of deploying your "Real or AI?" application to Vercel.

## Prerequisites

1. A [GitHub](https://github.com/) account
2. A [Vercel](https://vercel.com/) account (you can sign up with your GitHub account)
3. [Git](https://git-scm.com/) installed on your computer

## Step 1: Prepare Your Project for Deployment

The project already includes a `vercel.json` configuration file that tells Vercel how to deploy your static site.

## Step 2: Initialize a Git Repository

If your project isn't already in a Git repository, initialize one:

```bash
cd real-or-ai
git init
git add .
git commit -m "Initial commit"
```

## Step 3: Create a GitHub Repository

1. Go to [GitHub](https://github.com/) and log in
2. Click the "+" icon in the top-right corner and select "New repository"
3. Name your repository (e.g., "real-or-ai")
4. Keep it public or private as you prefer
5. Click "Create repository"
6. Follow the instructions to push your existing repository to GitHub:

```bash
git remote add origin https://github.com/YOUR_USERNAME/real-or-ai.git
git branch -M main
git push -u origin main
```

## Step 4: Deploy to Vercel

### Option 1: Deploy via Vercel Dashboard (Recommended for First-Time Deployment)

1. Go to [Vercel](https://vercel.com/) and log in with your GitHub account
2. Click "Add New..." and select "Project"
3. Import your GitHub repository (you may need to configure GitHub access)
4. Keep the default settings (Vercel will detect it's a static site)
5. Click "Deploy"

### Option 2: Deploy via Vercel CLI

1. Install the Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. Log in to Vercel:
   ```bash
   vercel login
   ```

3. Deploy your project:
   ```bash
   cd real-or-ai
   vercel
   ```

4. Follow the prompts to complete the deployment

## Step 5: Configure Your Domain (Optional)

1. In the Vercel dashboard, go to your project
2. Click on "Settings" > "Domains"
3. Add your custom domain and follow the instructions to configure DNS

## Updating Your Deployment

Whenever you make changes to your project:

1. Commit your changes to Git:
   ```bash
   git add .
   git commit -m "Description of changes"
   ```

2. Push to GitHub:
   ```bash
   git push
   ```

3. Vercel will automatically deploy the updated version

## Future Enhancements for Production

For a full production version with backend functionality:

1. **Implement a Backend API**:
   - Create a `/api` directory with serverless functions
   - Use Node.js with Express for API routes
   - Vercel will automatically detect and deploy these as serverless functions

2. **Database Integration**:
   - Set up MongoDB Atlas or another cloud database
   - Add environment variables in Vercel for database connection

3. **Image Storage**:
   - Set up AWS S3 or similar for image storage
   - Add environment variables for storage access

4. **Scheduled Tasks**:
   - Use Vercel Cron Jobs for the midnight reveal functionality
   - Configure in `vercel.json` with the `crons` property

5. **Email Functionality**:
   - Integrate with SendGrid, Mailchimp, or similar
   - Set up environment variables for API keys

## Troubleshooting

- **Build Errors**: Check the build logs in Vercel dashboard
- **Routing Issues**: Verify your `vercel.json` configuration
- **API Errors**: Check the function logs in Vercel dashboard

For more help, refer to the [Vercel Documentation](https://vercel.com/docs).
