// Example serverless function for revealing results at midnight EST
// This would be deployed as a scheduled function on Vercel
// Configure in vercel.json with:
// "crons": [{ "path": "/api/reveal-results", "schedule": "0 0 * * *" }]

module.exports = async (req, res) => {
  // This function would be triggered by Vercel Cron at midnight EST

  try {
    // In a real implementation, you would:
    // 1. Connect to your database
    // 2. Find the challenge for yesterday
    // 3. Update its status to "revealed"
    // 4. Optionally send emails to subscribers with the results

    // For demonstration purposes, we'll log what would happen
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayFormatted = yesterday.toISOString().split('T')[0];

    console.log(`Revealing results for challenge on ${yesterdayFormatted}`);
    console.log('This would update the database and send emails to subscribers');

    // If this function is called directly via HTTP, return a response
    if (res) {
      return res.status(200).json({
        success: true,
        message: 'Results revealed successfully',
        date: yesterdayFormatted
      });
    }

    // If called as a background function, just return
    return {
      success: true,
      message: 'Results revealed successfully',
      date: yesterdayFormatted
    };
  } catch (error) {
    console.error('Error revealing results:', error);
    
    if (res) {
      return res.status(500).json({ error: 'Internal server error' });
    }
    
    return { error: 'Internal server error' };
  }
};
