class PaymentsController < ApplicationController
  before_action :authenticate_user!

  def create
    stripe_publishable_key = Rails.application.credentials.publishable_key
    stripe_secret_key = Rails.application.credentials.secret_key
    # StripeConfig.setup
    Stripe.api_key = Rails.application.credentials.secret_key ||ENV['STRIPE_SECRET_KEY']
    listing_id = params[:listing_id]
    user_id = current_user.id
    listing = Listing.find(listing_id)
    order = Order.new(listing_id: listing.id, user_id: user_id, state: 2 )
    order.save
    begin
      # Create a Payment Intent
      intent = Stripe::PaymentIntent.create({
        amount: (listing.asking_price * 100).to_i || 100000 ,
        currency: 'usd',
        payment_method_types: ['card'],
        setup_future_usage: 'off_session',
      })


      render json: { intent: intent,
                     client_secret: intent.client_secret,
                     order_id: order.id
      }
    rescue Stripe::StripeError => e
      render json: { error: e.message }, status: :unprocessable_entity
    end
  end

  def payment_success
    listing_id = params[:listing_id]
    listing = Listing.find(listing_id)
    listing.update(status: 1) if listing.present?
    order_id = params[:order_id]
    order = Order.find(order_id)
    order.update(state: 1) if order.present?
  end
end
