require 'rails_helper'

RSpec.describe User, type: :model do
  it 'must have a unique email' do
    user = User.new(email: '<EMAIL>', password: 'test1234', password_confirmation: 'test1234')
    user2 = User.new(email: '<EMAIL>', password: 'test1234', password_confirmation: 'test1234')
    user.save!
    expect(user2).to_not be_valid
  end
  it 'must have an email formatted correctly' do
    user = User.new(email: 'test1234', password: 'test1234', password_confirmation: 'test1234')
    expect(user).to_not be_valid
  end
  it 'can be updated using the update_email_or_name method' do
    user = User.create!(email: '<EMAIL>', name: '<PERSON>', password: 'test1234', password_confirmation: 'test1234')
    user.update_email_or_name({ name: '<PERSON>' })
    user.update_email_or_name({ email: '<EMAIL>' })

    expect(user.reload.name).to eq('John')
    expect(user.reload.email).to eq('<EMAIL>')
  end
  it 'can create a new address for the user if one does not exist' do
    user = User.create!(email: '<EMAIL>', name: '<PERSON>', password: 'test1234', password_confirmation: 'test1234')
    user.update_or_create_address({ address_attributes: { street_address: '1234 Test St', city: 'Test City', state: 'Test State', zipcode: '12345' } })
    expect(user.address.street_address).to eq('1234 Test St')
  end
  it 'can update an existing address for the user' do
    user = User.create!(email: '<EMAIL>', name: 'Bob', password: 'test1234', password_confirmation: 'test1234')
    user.update_or_create_address({ address_attributes: { street_address: '1234 Test St', city: 'Test City', state: 'Test State', zipcode: '12345' } })
    user.update_or_create_address({ address_attributes: { id: user.address.id, street_address: '4567 Test St', city: 'Test City', state: 'Test State', zipcode: '12345' } })
    expect(user.address.street_address).to eq('4567 Test St')
  end
end
