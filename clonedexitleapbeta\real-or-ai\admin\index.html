<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Real or AI?</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="admin-header">
            <h1>Admin Dashboard</h1>
            <button id="logout-btn" class="vote-btn ai-btn">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>
        
        <div class="admin-nav">
            <a href="index.html" class="active">Dashboard</a>
            <a href="upload.html">Upload New Image</a>
            <a href="schedule.html">Schedule</a>
        </div>
        
        <div class="admin-container">
            <h2>Current Status</h2>
            
            <div class="status-section">
                <h3>Today's Challenge</h3>
                <div class="status-card">
                    <div class="image-preview-container">
                        <img src="../images/placeholder.jpg" alt="Today's image" class="image-preview">
                    </div>
                    <div class="status-details">
                        <p><strong>Date:</strong> <span id="today-date">May 1, 2025</span></p>
                        <p><strong>Correct Answer:</strong> <span id="today-answer">AI-Generated</span></p>
                        <p><strong>Votes so far:</strong> <span id="today-votes">127</span></p>
                        <p><strong>Real Votes:</strong> <span id="today-real-votes">52</span></p>
                        <p><strong>AI Votes:</strong> <span id="today-ai-votes">75</span></p>
                    </div>
                </div>
            </div>
            
            <div class="status-section">
                <h3>Yesterday's Results</h3>
                <div class="status-card">
                    <div class="image-preview-container">
                        <img src="../images/placeholder.jpg" alt="Yesterday's image" class="image-preview">
                    </div>
                    <div class="status-details">
                        <p><strong>Date:</strong> <span id="yesterday-date">April 30, 2025</span></p>
                        <p><strong>Correct Answer:</strong> <span id="yesterday-answer">Real</span></p>
                        <p><strong>Total Votes:</strong> <span id="yesterday-votes">1,250</span></p>
                        <p><strong>Real Votes:</strong> <span id="yesterday-real-votes">750</span></p>
                        <p><strong>AI Votes:</strong> <span id="yesterday-ai-votes">500</span></p>
                        <p><strong>Correct Percentage:</strong> <span id="yesterday-correct">60%</span></p>
                    </div>
                </div>
            </div>
            
            <div class="status-section">
                <h3>Upcoming Challenges</h3>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Image</th>
                            <th>Answer</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="upcoming-table-body">
                        <tr>
                            <td>May 2, 2025</td>
                            <td><img src="../images/placeholder.jpg" alt="Upcoming image" class="table-image"></td>
                            <td>Real</td>
                            <td>
                                <button class="table-btn edit-btn"><i class="fas fa-edit"></i></button>
                                <button class="table-btn delete-btn"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td>May 3, 2025</td>
                            <td><img src="../images/placeholder.jpg" alt="Upcoming image" class="table-image"></td>
                            <td>AI</td>
                            <td>
                                <button class="table-btn edit-btn"><i class="fas fa-edit"></i></button>
                                <button class="table-btn delete-btn"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div style="text-align: center; margin-top: 1rem;">
                    <a href="upload.html" class="vote-btn real-btn" style="text-decoration: none; display: inline-block;">
                        <i class="fas fa-plus"></i> Add New Challenge
                    </a>
                </div>
            </div>
            
            <div class="status-section">
                <h3>Email Subscribers</h3>
                <p><strong>Total Subscribers:</strong> <span id="total-subscribers">42</span></p>
                <button id="export-subscribers" class="vote-btn real-btn">
                    <i class="fas fa-download"></i> Export Subscriber List
                </button>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2025 Real or AI Challenge - Admin Panel</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if admin is logged in
            const isLoggedIn = localStorage.getItem('adminLoggedIn') === 'true';
            
            if (!isLoggedIn) {
                // Redirect to login page if not logged in
                window.location.href = 'login.html';
                return;
            }
            
            // Logout button handler
            document.getElementById('logout-btn').addEventListener('click', function() {
                localStorage.removeItem('adminLoggedIn');
                window.location.href = 'login.html';
            });
            
            // In a real app, this data would come from a server
            // Mock data for demonstration
            const dashboardData = {
                today: {
                    date: 'May 1, 2025',
                    answer: 'AI-Generated',
                    totalVotes: 127,
                    realVotes: 52,
                    aiVotes: 75
                },
                yesterday: {
                    date: 'April 30, 2025',
                    answer: 'Real',
                    totalVotes: 1250,
                    realVotes: 750,
                    aiVotes: 500
                },
                subscribers: 42
            };
            
            // Update the UI with the dashboard data
            document.getElementById('today-date').textContent = dashboardData.today.date;
            document.getElementById('today-answer').textContent = dashboardData.today.answer;
            document.getElementById('today-votes').textContent = dashboardData.today.totalVotes;
            document.getElementById('today-real-votes').textContent = dashboardData.today.realVotes;
            document.getElementById('today-ai-votes').textContent = dashboardData.today.aiVotes;
            
            document.getElementById('yesterday-date').textContent = dashboardData.yesterday.date;
            document.getElementById('yesterday-answer').textContent = dashboardData.yesterday.answer;
            document.getElementById('yesterday-votes').textContent = dashboardData.yesterday.totalVotes.toLocaleString();
            document.getElementById('yesterday-real-votes').textContent = dashboardData.yesterday.realVotes.toLocaleString();
            document.getElementById('yesterday-ai-votes').textContent = dashboardData.yesterday.aiVotes.toLocaleString();
            
            // Calculate correct percentage
            const correctVotes = dashboardData.yesterday.answer === 'Real' ? 
                dashboardData.yesterday.realVotes : dashboardData.yesterday.aiVotes;
            const correctPercentage = Math.round((correctVotes / dashboardData.yesterday.totalVotes) * 100);
            document.getElementById('yesterday-correct').textContent = correctPercentage + '%';
            
            document.getElementById('total-subscribers').textContent = dashboardData.subscribers;
            
            // Export subscribers button handler
            document.getElementById('export-subscribers').addEventListener('click', function() {
                alert('In a real app, this would download a CSV file of subscribers.');
            });
            
            // Edit and delete button handlers for upcoming challenges
            const editButtons = document.querySelectorAll('.edit-btn');
            const deleteButtons = document.querySelectorAll('.delete-btn');
            
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const row = this.closest('tr');
                    const date = row.cells[0].textContent;
                    alert(`In a real app, this would open an edit form for the challenge on ${date}`);
                });
            });
            
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const row = this.closest('tr');
                    const date = row.cells[0].textContent;
                    if (confirm(`Are you sure you want to delete the challenge for ${date}?`)) {
                        alert(`In a real app, this would delete the challenge for ${date}`);
                        // row.remove(); // This would remove the row from the table
                    }
                });
            });
        });
    </script>
    
    <style>
        .status-section {
            margin-bottom: 2rem;
        }
        
        .status-card {
            display: flex;
            flex-direction: column;
            background-color: var(--light-color);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .image-preview-container {
            margin-bottom: 1rem;
        }
        
        .status-details p {
            margin-bottom: 0.5rem;
        }
        
        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .admin-table th, .admin-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid var(--gray-color);
        }
        
        .table-image {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .table-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            margin-right: 0.5rem;
        }
        
        .edit-btn {
            color: var(--primary-color);
        }
        
        .delete-btn {
            color: var(--danger-color);
        }
        
        @media (min-width: 768px) {
            .status-card {
                flex-direction: row;
                gap: 2rem;
            }
            
            .image-preview-container {
                flex: 0 0 300px;
                margin-bottom: 0;
            }
            
            .status-details {
                flex: 1;
            }
        }
    </style>
</body>
</html>
