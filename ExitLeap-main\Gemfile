source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "3.1.2"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 7.0.8"

# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"

# Use the Puma web server [https://github.com/puma/puma]
gem "puma", "~> 5.0"

gem 'devise', '~> 4.7'

gem 'devise-jwt', '~> 0.7'

gem 'rack-cors', require: 'rack/cors'

gem 'jsonapi-serializer', '~> 2.2.0'

gem 'attr_encrypted', '~> 4.0.0'

gem 'stripe', '~> 10.6.0'

gem "paperclip", '~> 6.1.0'
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder", '~> 2.11.5'

# Use Redis adapter to run Action Cable in production
gem 'redis', '~> 4.0'

gem 'websocket-rails', '~> 0.7.0'

gem 'eventmachine', '~> 1.2.7'

gem 'thin', '~> 1.8.2'

gem 'hiredis', '~> 0.6.3'

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ mingw mswin x64_mingw jruby ]

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
# gem "image_processing", "~> 1.2"

# Use Rack CORS for handling Cross-Origin Resource Sharing (CORS), making cross-origin AJAX possible
# gem "rack-cors"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri mingw x64_mingw ]
  gem "rspec-rails"
  gem "factory_bot_rails"
  gem "faker"
  gem "shoulda-matchers"
  gem "database_cleaner-active_record"
  gem "launchy"
  gem "webdrivers"
  gem "simplecov"
  gem "rubocop"
  gem "rubycritic", require: false

end

group :development do
  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
end

gem "forest_liana", "~> 8.0"
