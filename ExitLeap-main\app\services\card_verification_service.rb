class CardVerificationService
  def self.verify_card(current_user, card_token)
    return { error: 'Stripe token is missing' }, :unprocessable_entity if card_token.blank?

    begin
      Stripe.api_key = Rails.application.credentials.secret_key ||ENV['STRIPE_SECRET_KEY']

      card = Stripe::Token.retrieve(card_token)

      if card.livemode && card.used
        return { error: 'Card token has already been used' }, :unprocessable_entity
      elsif card.card.exp_year < Time.now.year ||
            (card.card.exp_year == Time.now.year && card.card.exp_month < Time.now.month)
        return { error: 'Card is expired' }, :unprocessable_entity
      else

        current_user.update!(stripe_card_token: card_token)
        current_user.save!
        return { value: 'Card saved successfully' }, :ok
      end
    rescue Stripe::StripeError => e
      return { error: e.message }, :unprocessable_entity
    end
  end
end
