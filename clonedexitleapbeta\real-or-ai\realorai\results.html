<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yesterday's Results - Real or AI?</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Real or AI?</h1>
            <p class="subtitle">Yesterday's Results</p>
        </header>

        <main>
            <div class="results-container">
                <div class="image-container">
                    <img id="result-image" src="images/placeholder.jpg" alt="Yesterday's challenge image">
                    <div class="image-date" id="result-date">April 30, 2025</div>
                </div>

                <div class="result-answer answer-real" id="result-answer">
                    <i class="fas fa-camera"></i> This image was REAL!
                </div>

                <div class="voting-stats">
                    <div class="stat-box">
                        <div class="stat-value" id="total-votes">1,250</div>
                        <div class="stat-label">Total Votes</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-value" id="correct-percentage">60%</div>
                        <div class="stat-label">Got it Right</div>
                    </div>
                </div>

                <div class="voting-stats">
                    <div class="stat-box">
                        <div class="stat-value" id="real-votes">750</div>
                        <div class="stat-label">Voted Real</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-value" id="ai-votes">500</div>
                        <div class="stat-label">Voted AI</div>
                    </div>
                </div>

                <div style="margin-top: 2rem; text-align: center;">
                    <a href="index.html" class="vote-btn real-btn" style="text-decoration: none; display: inline-block;">
                        <i class="fas fa-home"></i> Back to Today's Challenge
                    </a>
                </div>
            </div>

            <div class="subscription-container">
                <p>Want to receive daily results?</p>
                <form id="subscription-form">
                    <input type="email" id="email" placeholder="Enter your email" required>
                    <button type="submit">Subscribe</button>
                </form>
                <p class="subscription-message" id="subscription-message"></p>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 Real or AI Challenge</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // In a real app, this data would come from a server
            const resultData = {
                image: 'images/placeholder.jpg',
                date: 'April 30, 2025',
                correctAnswer: 'real',
                totalVotes: 1250,
                realVotes: 750,
                aiVotes: 500
            };

            // Update the UI with the result data
            document.getElementById('result-image').src = resultData.image;
            document.getElementById('result-date').textContent = resultData.date;
            
            const resultAnswer = document.getElementById('result-answer');
            if (resultData.correctAnswer === 'real') {
                resultAnswer.innerHTML = '<i class="fas fa-camera"></i> This image was REAL!';
                resultAnswer.className = 'result-answer answer-real';
            } else {
                resultAnswer.innerHTML = '<i class="fas fa-robot"></i> This image was AI-GENERATED!';
                resultAnswer.className = 'result-answer answer-ai';
            }
            
            document.getElementById('total-votes').textContent = resultData.totalVotes.toLocaleString();
            document.getElementById('real-votes').textContent = resultData.realVotes.toLocaleString();
            document.getElementById('ai-votes').textContent = resultData.aiVotes.toLocaleString();
            
            // Calculate correct percentage
            const correctVotes = resultData.correctAnswer === 'real' ? resultData.realVotes : resultData.aiVotes;
            const correctPercentage = Math.round((correctVotes / resultData.totalVotes) * 100);
            document.getElementById('correct-percentage').textContent = correctPercentage + '%';
            
            // Subscription form handler
            const subscriptionForm = document.getElementById('subscription-form');
            const subscriptionMessage = document.getElementById('subscription-message');
            
            subscriptionForm.addEventListener('submit', function(event) {
                event.preventDefault();
                
                const email = event.target.elements.email.value;
                
                // In a real app, this would send the email to a server
                console.log(`Subscription email: ${email}`);
                
                // Show success message
                subscriptionMessage.textContent = 'Thank you for subscribing! You will receive daily results.';
                subscriptionMessage.style.color = 'var(--secondary-color)';
                
                // Reset form
                event.target.reset();
                
                // Clear message after 5 seconds
                setTimeout(() => {
                    subscriptionMessage.textContent = '';
                }, 5000);
            });
        });
    </script>
</body>
</html>
