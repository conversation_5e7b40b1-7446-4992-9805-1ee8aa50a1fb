# app/controllers/listings_controller.rb
class ListingsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_listing, only: [:show]
  before_action :authorize_seller, only: [:create, :update, :destroy]

  def authenticate_user!
    super
  end

  def index
    if current_user.role == 'seller'
      @listings = Listing.where(user_id: current_user.id)
    else
      has_premium_subscription = current_user.premium_subscription.present?
      if has_premium_subscription
        @listings = Listing.all
      else
        @listings = Listing.where(premium: false)
      end
    end

    render json: @listings
  end

  def show
    documents_with_urls = @listing.documents.map do |document|
      {
        id: document.id,
        listing_id: document.listing_id,
        created_at: document.created_at,
        updated_at: document.updated_at,
        file_url: document.file_url
      }
    end
    response_data = @listing.as_json.merge(documents: documents_with_urls, address: @listing.address, competitors: @listing.competitors, ownership: @listing.ownerships, growth_opportunities: @listing.growth_opportunity )
    render json: response_data
  end

  def filter_and_sort
    if current_user.role == 'seller'
      @listings = Listing.all.where(user_id: current_user.id)
      @listings = filter_listings(@listings, params)
    else
      @listings = Listing.all
      @listings = filter_listings(@listings, params)
    end
    render json: @listings
  end

  def sort
    if current_user.role == 'seller'
      @listings = Listing.all.where(user_id: current_user.id)
       @listings = sort_listings(@listings, params)
    else
      @listings = Listing.all
       @listings = sort_listings(@listings, params)
    end
    render json: @listings
  end

  def create
    puts "current user", current_user
    @listing = current_user.listings.new(listing_params)
    @listing.user_id = current_user.id
    if @listing.save
      set_listing_state(@listing)
      if params[:document_data].present?
        params[:document_data].each do |doc|
          @listing.documents.create(file: doc)
        end
        @listing.reload
        documents_with_urls = @listing.documents.map do |document|
          {
            id: document.id,
            listing_id: document.listing_id,
            created_at: document.created_at,
            updated_at: document.updated_at,
            file_url: document.file_url
          }
        end
        response_data = @listing.as_json.merge(documents: documents_with_urls)
        render json: response_data, status: :created
      else
        render json: @listing, status: :created
      end
    else
      render json: @listing.errors, status: :unprocessable_entity
    end
  end

  def update
    @listing = Listing.find_by(id: params[:id])
    if @listing.update(listing_params.except(:competitors_attributes, :ownerships_attributes, :growth_opportunity_attributes, :address_attributes, :document_data))

      if params[:step] == 'four' && params[:listing][:competitors_attributes].present?
        update_or_create_competitors(@listing, params[:listing][:competitors_attributes])
      end

      if params[:step] == 'five' && params[:listing][:ownerships_attributes].present?
        create_or_update_ownerships(@listing, params[:listing][:ownerships_attributes])
      end
      if (params[:step] == 'six') && params[:listing][:growth_opportunity_attributes].present?
        update_growth_opportunities(@listing, params[:listing][:growth_opportunity_attributes])
      end

      update_listing_address(@listing, params[:listing][:address_attributes]) if params[:listing].present? && params[:listing][:address_attributes].present?
      set_listing_state(@listing)

      if params[:document_data].present?
        update_or_create_documents(@listing, params[:document_data], params[:document_type])
      end
      documents_with_urls = @listing.documents.map do |document|
        {
          id: document.id,
          listing_id: document.listing_id,
          created_at: document.created_at,
          updated_at: document.updated_at,
          file_url: document.file_url
        }
      end
      response_data = @listing.as_json.merge(documents: documents_with_urls, address: @listing.address, competitors: @listing.competitors, ownership: @listing.ownerships, growth_opportunities: @listing.growth_opportunity )
      render json: response_data
    else
      render json: @listing.errors, status: :unprocessable_entity
    end
  end

  def set_listing_state(listing)
    case params[:step]
    when 'zero'
      listing.update(state: 'step_1')
    when 'one'
       listing.update(state: 'step_2')
    when 'two'
       listing.update(state: 'step_3')
    when 'three'
       listing.update(state: 'step_4')
    when 'four'
       listing.update(state: 'step_5')
    when 'five'
       listing.update(state: 'step_6')
    when 'six'
      listing.update(state: 'step_7')
    when 'seven'
       listing.update(state: 'step_8')
    when 'eight'
      listing.update(state: 'complete')
    when 'document'
      listing.update(state: 'document')
    end
  end

  def listing_potential_buyers
    @listing = Listing.find(params[:id])
    industry = @listing.industry
    location = @listing.location
    potential_buyers = User.buyer
                        .joins(buying_offers: :listing)
                        .where('buying_offers.state = ? AND listings.industry in (?)', BuyingOffer.states[:pending], industry)
                        .distinct

    render json: potential_buyers
  end

  def potential_buyers
    potential_buyers = User.buyer
                        .joins(buying_offers: :listing)
                        .where('buying_offers.state = ? AND listings.industry in (?)', BuyingOffer.states[:pending], current_user.listings.pluck(:industry))
                        .distinct

    render json: potential_buyers
  end

  def destroy
    @listing = Listing.find_by(id: params[:id])
    @listing.destroy
    head :no_content
  end

  def save_attachments(params)
    params[:document_data].each do |doc|
      self.documents.create(:file => doc)
    end
  end

  def create_buying_offer
    @listing = Listing.find(params[:listing_id])
    @buying_offer = @listing.buying_offers.new(buying_offer_params)
    @buying_offer.user = current_user
    @buying_offer.state = :pending

    if @buying_offer.save
      render json: @buying_offer, status: :created
    else
      render json: @buying_offer.errors, status: :unprocessable_entity
    end
  end

  def accept_buying_offer
    @buying_offer = BuyingOffer.find(params[:id])
    @listing = Listing.find(@buying_offer.listing_id)
    @buyer = User.find(@buying_offer.user_id)
    @seller = User.find(@listing.user_id)
    @buying_offer.accepted!
    # stripe_service = FindOrCreateCustomer.call(@buyer)
    # p stripe_service
    # # stripe_customer = stripe_service.call(fetch_payment_methods: true)
    # @buyer.update(stripe_customer_id: stripe_service.id)
    # subscription_service = SubscriptionService.new(@buyer, @seller, @listing, @buying_offer)
    # p 'the subscription service is', subscription_service
    # result = subscription_service.create_subscription
    # p 'the subscription service results are', result
    # if result == 'Subscription created successfully.'
    #   current_user.seller_stripe.update(available_balance: new_balance_value)
    #   render json: @buying_offer, status: 200,  result: result
    # else
    #   render json: { result: result, 'buying_offer' => @buying_offer, 'listing' => @listing, 'buyer' => @buyer, 'listing_seller' => @seller, 'buyer_stripe_customer' => stripe_service }
    # end
    render json: @buying_offer, status: 200
  end

  def reject_buying_offer
    @buying_offer = BuyingOffer.find(params[:id])
    @buying_offer.rejected!
    render json: @buying_offer
  end

  def remove_document
    document = Document.find_by(id: params[:document_id])
    unless document
      render json: { error: 'Document not found' }, status: :not_found
      return
    end
    unless document.destroy
      render json: { error: 'Failed to remove document' }, status: :unprocessable_entity
      return
    end
    render json: { message: 'Document removed successfully' }
  end

  def fetch_documents_by_statement_type
    listing_id = params[:id]
    request_body = JSON.parse(request.body.read)
    statement_type = request_body['statement_type']

    if listing_id.present? && statement_type.present?
      listing = Listing.find_by(id: listing_id)

      if listing.present?
        documents = listing.documents.where(statement_type: statement_type)

        documents_with_urls = documents.map do |document|
          {
            id: document.id,
            listing_id: document.listing_id,
            created_at: document.created_at,
            updated_at: document.updated_at,
            file_url: document.file_url
          }
        end

        render json: documents_with_urls
      else
        render json: { error: 'Listing not found' }, status: :not_found
      end
    else
      render json: { error: 'Invalid parameters' }, status: :unprocessable_entity
    end
  end


  private

  def update_or_create_documents(listing, document_data, document_type)
    document_data.each do |doc|
      if doc.respond_to?(:tempfile) && doc.respond_to?(:original_filename)
        # puts "Processing UploadedFile: #{doc.original_filename}"
        # puts doc.respond_to?(:original_filename)
        # puts doc.respond_to?(:tempfile)
        original_filename = doc.original_filename
        existing_document = listing.documents.find_by(original_filename: original_filename, statement_type: document_type)

        if existing_document
          existing_document.file.purge
          existing_document.file.attach(doc)
          existing_document.update(statement_type: document_type)  # Ensure correct statement_type
        else
          listing.documents.create(file: doc, original_filename: original_filename, statement_type: document_type)
        end
      elsif doc.is_a?(Hash) && doc.key?(:id)
        # puts "Processing Existing Document with ID: #{doc[:id]}"
        document_id = doc[:id]
        existing_document = listing.documents.find_by(id: document_id, statement_type: document_type)

        if existing_document
          existing_document.file.purge
          existing_document.file.attach(doc[:file])
          existing_document.update(statement_type: document_type)  # Ensure correct statement_type
        end
      end
    end
  end


  def filter_by_industry(listings, industry)
    listings.where(industry: industry)
  end

  def filter_by_price(listings, min_price, max_price)
    filtered_listings = listings
    filtered_listings = filtered_listings.where('asking_price >= ?', min_price.to_f) if min_price.present?
    filtered_listings = filtered_listings.where('asking_price <= ?', max_price.to_f) if max_price.present?
    filtered_listings
  end

  def filter_by_location(listings, location)
    listings.where(location: location)
  end

  def filter_by_created(listings, newly_created, oldest_created)
    if newly_created.present?
      listings.order(created_at: :desc)
    elsif oldest_created.present?
      listings.order(created_at: :asc)
    else
      listings
    end
  end

  def filter_by_asking_price(listings, highest, lowest)
    if highest.present?
      listings.order(asking_price: :desc)
    elsif lowest.present?
      listings.order(asking_price: :asc)
    else
      listings
    end
  end

  def filter_by_ttm_revenue(listings, highest, lowest)
    if highest.present?
      listings.order(ttm_revenue: :desc)
    elsif lowest.present?
      listings.order(ttm_revenue: :asc)
    else
      listings
    end
  end

  def filter_by_date_founded(listings, date_range)
    case date_range
    when '1-3'
      listings.where('date_founded >= ? AND date_founded <= ?', 1.year.ago, 3.years.ago)
    when '3-6'
      listings.where('date_founded >= ? AND date_founded <= ?', 3.years.ago, 6.years.ago)
    when '6-9'
      listings.where('date_founded >= ? AND date_founded <= ?', 6.years.ago, 9.years.ago)
    when '10+'
      listings.where('date_founded <= ?', 10.years.ago)
    else
      listings
    end
  end

  def update_or_create_competitors(listing, competitors_attributes)
    competitors_attributes.each do |competitor_params|
      competitor_id = competitor_params[:id] if competitor_params.present?
      if competitor_id.present?
        # Update existing competitor
        existing_competitor = listing.competitors.find_by(id: competitor_id)
        existing_competitor.update(competitor_params.permit(:name, :website).to_h.except(:_destroy)) if existing_competitor
      else
        listing.competitors.create(competitor_params.permit(:name, :website).to_h)
      end
    end
  end

  def competitor_params
    params.permit( :name, :website)
  end

  def competitors_attributes
    params.require(:listing).permit(competitors_attributes: [:id, :name, :website, :_destroy])
  end

  def create_or_update_ownerships(listing, ownerships_attributes)
    ownerships_attributes.each do |ownership_params|
      ownership_id = ownership_params[:id] if ownership_params.present?
      if ownership_id.present?
        existing_ownership = listing.ownerships.find_by(id: ownership_id)
        existing_ownership.update(ownership_params.permit(:firstname, :middlename, :lastname, :email, :phone, :ownership))
      else
        listing.ownerships.create(ownership_params.permit(:firstname, :middlename, :lastname, :email, :phone, :ownership))
      end
    end
  end

  def update_growth_opportunities(listing, growth_opportunities_params)
    growth_opportunities_params.each do |opportunity_params|
      opportunity_id = opportunity_params[:id]
      if opportunity_id.present?
        growth_opportunity = GrowthOpportunity.find(opportunity_id)
        growth_opportunity.update(opportunity_params.permit(:description, opportunity: []).except(:_destroy))
      else
        listing.create_growth_opportunity(opportunity_params.permit(:description, opportunity: []))
      end
    end
  end

  def update_listing_address(listing, address_params)
    return unless address_params.present?
    existing_address = listing.address
    if existing_address
      updated_address = existing_address.update(permitted_address_params(address_params))
    else
      new_address = Address.create(permitted_address_params(address_params))
      new_address.user_id = listing.user_id
      new_address.save!
      listing.update(address_id: new_address.id)
      updated_address = new_address
    end
    updated_address
  end

  def permitted_address_params(params)
    params.permit(:street_address, :address_line_2, :city, :state, :zipcode)
  end


  def filter_listings(listings, params)
    filters = params.slice(:industry, :min_price, :max_price, :location, :newly_created, :oldest_created, :asking_price_highest, :asking_price_lowest, :ttm_revenue_highest, :ttm_revenue_lowest, :date_founded)
    listings = filter_by_industry(listings, filters[:industry]) if filters[:industry].present?
    listings = filter_by_price(listings, filters[:min_price], filters[:max_price]) if filters[:min_price].present? || filters[:max_price].present?
    listings = filter_by_location(listings, filters[:location]) if filters[:location].present?
    # listings = filter_by_asking_price(listings, filters[:asking_price_highest], filters[:asking_price_lowest]) if filters[:asking_price_highest].present? || filters[:asking_price_lowest].present?
    # listings = filter_by_created(listings, filters[:newly_created], filters[:oldest_created]) if filters[:newly_created].present? || filters[:oldest_created].present?
    # listings = filter_by_ttm_revenue(listings, filters[:ttm_revenue_highest], filters[:ttm_revenue_lowest]) if filters[:ttm_revenue_highest].present? || filters[:ttm_revenue_lowest].present?
    listings = filter_by_date_founded(listings, filters[:date_founded]) if filters[:date_founded].present?
    sort_listings(listings, params)
    return listings
  end

  def sort_listings(listings, params)
    if params[:newly_created].present?
      listings = listings.order(created_at: :desc)
    elsif params[:oldest_created].present?
      listings = listings.order(created_at: :asc)
    elsif params[:asking_price_highest].present?
      listings = listings.order(asking_price: :desc)
    elsif params[:asking_price_lowest].present?
      listings = listings.order(asking_price: :asc)
    elsif params[:ttm_revenue_highest].present?
      listings = listings.order(ttm_revenue: :desc)
    end

    listings
  end


  def buying_offer_params
    params.require(:buying_offer).permit(:offer_price)
  end

  def set_listing
    @listing = Listing.find(params[:id])
  end

  def listing_params
    puts "Step: #{params[:step]}"
    puts :listing
    case params[:step]
    when 'one'
      params.require(:listing).permit(:title, :url, :role, :ttm_revenue, :ttm_profit, :industry, :state)
    when 'two'
      params.require(:listing).permit(:headline, :description, :business_model, :state)
    when 'three'
      params.require(:listing).permit(:date_founded, :location, :employees, :state, address_attributes: [:street_address, :address_line_2, :city, :state, :zipcode])
    when 'four'
      params.require(:listing).permit(competitors_attributes: [:id, :name, :website, :_destroy])
    when 'five'
      params.require(:listing).permit(ownerships_attributes:[:id, :firstname, :middlename, :lastname, :email, :phone, :ownership, :_destroy])
    when 'six'
      params.require(:listing).permit(growth_opportunity_attributes: [:id, :description, opportunity: []])
    when 'seven'
      params.require(:listing).permit(:asking_price, :asking_price_reason)
    when 'eight'
      params.require(:listing).permit(:premium)
    when 'document'
      params.permit(:document_data)
    else
      params.require(:listing).permit(:title, :url, :role, :ttm_revenue, :ttm_profit, :state)
    end
  end

  def authorize_seller
    unless current_user && current_user.seller?
      render json: { status: 'Unauthorized' }, status: :unauthorized
    end
  end
end
