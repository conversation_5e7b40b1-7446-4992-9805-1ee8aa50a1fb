// DOM Elements
const dailyImage = document.getElementById('daily-image');
const imageDate = document.getElementById('image-date');
const votingContainer = document.getElementById('voting-container');
const votedContainer = document.getElementById('voted-container');
const realBtn = document.getElementById('real-btn');
const aiBtn = document.getElementById('ai-btn');
const subscriptionForm = document.getElementById('subscription-form');
const subscriptionMessage = document.getElementById('subscription-message');

// Constants
const STORAGE_KEY_VOTE = 'realOrAI_vote';
const STORAGE_KEY_DATE = 'realOrAI_date';

// Mock data (in a real app, this would come from a server)
const mockData = {
    currentImage: {
        url: 'images/placeholder.jpg',
        date: new Date().toISOString(),
        correctAnswer: 'ai' // This would normally be hidden until reveal time
    }
};

// Initialize the app
function initApp() {
    setupCurrentDate();
    checkPreviousVote();
    setupEventListeners();
}

// Set up the current date display
function setupCurrentDate() {
    const today = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    imageDate.textContent = today.toLocaleDateString('en-US', options);
}

// Check if the user has already voted today
function checkPreviousVote() {
    const lastVoteDate = localStorage.getItem(STORAGE_KEY_DATE);
    const today = new Date().toDateString();
    
    if (lastVoteDate === today) {
        // User already voted today
        votingContainer.classList.add('hidden');
        votedContainer.classList.remove('hidden');
    } else {
        // User hasn't voted today
        votingContainer.classList.remove('hidden');
        votedContainer.classList.add('hidden');
    }
}

// Set up event listeners
function setupEventListeners() {
    // Voting buttons
    realBtn.addEventListener('click', () => handleVote('real'));
    aiBtn.addEventListener('click', () => handleVote('ai'));
    
    // Subscription form
    subscriptionForm.addEventListener('submit', handleSubscription);
}

// Handle vote submission
function handleVote(vote) {
    // In a real app, this would send the vote to a server
    console.log(`User voted: ${vote}`);
    
    // Save vote to local storage
    localStorage.setItem(STORAGE_KEY_VOTE, vote);
    localStorage.setItem(STORAGE_KEY_DATE, new Date().toDateString());
    
    // Redirect to thank you page
    window.location.href = 'thanks.html';
}

// Handle email subscription
function handleSubscription(event) {
    event.preventDefault();
    
    const email = event.target.elements.email.value;
    
    // In a real app, this would send the email to a server
    console.log(`Subscription email: ${email}`);
    
    // Show success message
    subscriptionMessage.textContent = 'Thank you for subscribing! You will receive daily results.';
    subscriptionMessage.style.color = 'var(--secondary-color)';
    
    // Reset form
    event.target.reset();
    
    // Clear message after 5 seconds
    setTimeout(() => {
        subscriptionMessage.textContent = '';
    }, 5000);
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', initApp);

// Mock functions for admin panel (these would be in separate admin JS files)

// Function to upload a new image (mock)
function uploadImage(imageFile, correctAnswer, scheduledDate) {
    // This would upload the image to a server and store the metadata
    console.log('Image uploaded:', {
        file: imageFile,
        answer: correctAnswer,
        date: scheduledDate
    });
    
    return {
        success: true,
        message: 'Image uploaded successfully'
    };
}

// Function to get voting statistics (mock)
function getVotingStats(date) {
    // This would fetch voting statistics from the server
    return {
        totalVotes: 1250,
        realVotes: 750,
        aiVotes: 500,
        correctAnswer: 'real',
        date: date
    };
}
