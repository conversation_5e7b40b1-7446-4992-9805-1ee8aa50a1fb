class FundraiseCampaignsController < ApplicationController
  before_action :authenticate_user!

  def index
    @campaigns = current_user.fundraise_campaigns
    if @campaigns.present?
      render json: @campaigns, include: { campaign_investors: { include: :user }, listing: {} }, status: :ok
    else
      render json: { status: 'No campaigns have been created.' }
    end
  end

  def create
    @listing = Listing.find(params[:listing_id])
     @campaign = current_user.fundraise_campaigns.build(fundraise_campaign_params)
    if @campaign.save
      selected_investors = params[:selected_investors]
      email_investors = selected_investors["emails"]
      user_id_investors = selected_investors["users"]

      failed_investors = []

      if email_investors
        email_investors.each do |email|
          investor = User.find_by(email: email)

          if investor
            @campaign.campaign_investors.create(investor: investor)
          else
            failed_investors << email
          end
        end
      end

      if user_id_investors
        user_id_investors.each do |user_id|
          investor = User.find_by(id: user_id)
          if investor
            @campaign.campaign_investors.create(user_id: investor.id, fundraise_campaign_id: @campaign.id)
            @campaign.save!
          else
            failed_investors << "User with ID: #{user_id}"
          end
        end
      end

      if failed_investors.empty?
       render json: @campaign, include: { campaign_investors: { include: :user } }, status: :created
      else
        error_message = "Failed to add the following investors: #{failed_investors.join(', ')}"
        render json: { error: error_message }, status: :unprocessable_entity
      end
    else
      render json: @campaign.errors, status: :unprocessable_entity
    end
  end

  private
  def fundraise_campaign_params
    params.require(:fundraise_campaign).permit(:title, :description, :listing_id, :selected_investors)
  end
end
