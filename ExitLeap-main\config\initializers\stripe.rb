def fetch_stripe_secret_key
  stripe_secret_key = ENV['STRIPE_SECRET_KEY']
  if stripe_secret_key.blank?
    credentials = Rails.application.credentials
    stripe_secret_key = credentials.stripe_secret_key if credentials.present? && credentials.stripe_secret_key.present?
  end
  stripe_secret_key
end

Rails.configuration.stripe = {
  publishable_key: ENV['STRIPE_PUBLIC_KEY'] || Rails.application.credentials.publishable_key,
  secret_key: ENV['STRIPE_SECRET_KEY'] || Rails.application.credentials.secret_key
}

Stripe.api_key = Rails.application.credentials.secret_key || ENV['STRIPE_SECRET_KEY']
