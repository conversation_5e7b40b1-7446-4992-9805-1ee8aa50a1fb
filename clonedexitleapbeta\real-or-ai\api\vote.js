// Example serverless function for handling votes
// This would be deployed as a serverless function on Vercel

module.exports = async (req, res) => {
  // Set CORS headers to allow requests from your frontend
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,POST');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
  );

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow POST requests for voting
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Parse the request body
    const { vote, userId } = req.body;

    // Validate the vote
    if (!vote || (vote !== 'real' && vote !== 'ai')) {
      return res.status(400).json({ error: 'Invalid vote. Must be "real" or "ai"' });
    }

    // In a real implementation, you would:
    // 1. Validate the user hasn't already voted today
    // 2. Store the vote in a database
    // 3. Update vote counts

    // For this example, we'll just return a success response
    console.log(`Received vote: ${vote} from user: ${userId || 'anonymous'}`);

    // Return a success response
    return res.status(200).json({
      success: true,
      message: 'Vote recorded successfully',
      data: {
        vote,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error processing vote:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};
