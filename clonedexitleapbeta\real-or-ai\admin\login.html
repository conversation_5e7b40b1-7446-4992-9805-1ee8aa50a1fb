<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Real or AI?</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: var(--light-color);
        }
        
        .login-container {
            background-color: white;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-form {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .login-message {
            text-align: center;
            margin-top: 1rem;
            color: var(--danger-color);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>Admin Login</h1>
            <p>Real or AI? Challenge</p>
        </div>
        
        <form id="login-form" class="login-form">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" class="vote-btn real-btn">
                <i class="fas fa-sign-in-alt"></i> Login
            </button>
        </form>
        
        <p id="login-message" class="login-message"></p>
        
        <div style="text-align: center; margin-top: 2rem;">
            <a href="../index.html">Back to Main Site</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const loginMessage = document.getElementById('login-message');
            
            loginForm.addEventListener('submit', function(event) {
                event.preventDefault();
                
                const username = event.target.elements.username.value;
                const password = event.target.elements.password.value;
                
                // In a real app, this would validate credentials against a server
                // For demo purposes, we'll use a hardcoded admin/admin123
                if (username === 'admin' && password === 'admin123') {
                    // Set a session token (in a real app, this would be a secure token from the server)
                    localStorage.setItem('adminLoggedIn', 'true');
                    
                    // Redirect to admin dashboard
                    window.location.href = 'index.html';
                } else {
                    loginMessage.textContent = 'Invalid username or password';
                }
            });
        });
    </script>
</body>
</html>
