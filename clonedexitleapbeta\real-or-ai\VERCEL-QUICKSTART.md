# Real or AI? - Vercel Quick Start Guide

This guide provides the essential steps to deploy your "Real or AI?" application to Vercel.

## Prerequisites

- A [GitHub](https://github.com/) account
- A [Vercel](https://vercel.com/) account (sign up with your GitHub account)

## Deployment Steps

### 1. Push to GitHub

```bash
# Initialize Git repository (if not already done)
git init
git add .
git commit -m "Initial commit"

# Create a new repository on GitHub and push your code
git remote add origin https://github.com/YOUR_USERNAME/real-or-ai.git
git branch -M main
git push -u origin main
```

### 2. Deploy to Vercel

1. Go to [Vercel](https://vercel.com/) and log in
2. Click "Add New..." > "Project"
3. Import your GitHub repository
4. Keep the default settings (Vercel will detect the configuration)
5. Click "Deploy"

That's it! Your static site is now deployed.

## Adding Backend Functionality

The project includes example API endpoints in the `/api` directory that demonstrate how to:

- <PERSON><PERSON> votes (`/api/vote.js`)
- Manage email subscriptions (`/api/subscribe.js`)
- Reveal results at midnight EST (`/api/reveal-results.js`)

To make these functional:

1. Set up a MongoDB database (e.g., [MongoDB Atlas](https://www.mongodb.com/cloud/atlas))
2. Configure environment variables in Vercel:
   - Go to your project settings
   - Navigate to "Environment Variables"
   - Add the variables from `.env.example`

## Vercel Features Used

- **Static Site Hosting**: For the HTML/CSS/JavaScript frontend
- **Serverless Functions**: For the API endpoints in `/api`
- **Cron Jobs**: For the midnight reveal functionality
- **Environment Variables**: For secure configuration

## Testing Your Deployment

After deployment, Vercel will provide a URL for your site (e.g., `https://real-or-ai.vercel.app`).

Visit this URL to see your application in action!

## Need More Help?

For more detailed instructions, refer to the [DEPLOYMENT.md](DEPLOYMENT.md) file.
