class PremiumSubscriptionService
  def initialize(buyer)
    @buyer = buyer
  end

  def create_subscription
    Stripe.api_key = Rails.application.credentials.secret_key || ENV['STRIPE_SECRET_KEY']
    begin
      customer = Stripe::Customer.retrieve(@buyer.stripe_customer_id)
      default_payment_method_id = customer.invoice_settings.default_payment_method
      stripe_premium_listing_prod = ENV['PREMIUM_LISTING_SUB'] || 'prod_P12qDod5yQ4nHg'
      listing_product = Stripe::Product.retrieve(stripe_premium_listing_prod)
      prices = Stripe::Price.list(product: listing_product.id)

      price = prices.data.first

      if price.present?
        subscription = Stripe::Subscription.create(
          customer: @buyer.stripe_customer_id,
          items: [
            {
              price: price,
            }
          ],
          default_payment_method: default_payment_method_id,
          metadata: {
            buyer_name: @buyer.name,
            buyer_email: @buyer.email,
            buyer_id: @buyer.id
          }
        )
        if subscription.present?
          # save_subscription(subscription)
          # p 'the stripe customer subscription is', subscription
          # sleep(1)
          # invoice =Stripe::Invoice.retrieve(subscription.latest_invoice)
          # sleep(1)
          # charge_id = invoice.charge
          # begin
          #   transfer = Stripe::Transfer.create({
          #     amount: (10 * 100).to_i,
          #     currency: 'usd',
          #     destination: 'acct_1O9v9LJINtBTDsnY',
          #     source_transaction: charge_id
          #   })
          # rescue Stripe::StripeError => e
          #   return "Stripe error occured in transfer error: #{e.message}", 'subscription' => subscription
          # rescue Stripe::InvalidRequestError => e
          #   return "Invalid request error: #{e.message}", 'subscription' => subscription
          # end
          # p 'the stripe admin transfer is', transfer

          # if transfer_error
          #   return { 'Subscription created successfully.' => subscription, 'Transfer error' => transfer_error }
          # else
          #   return { 'Subscription created successfully.' => subscription, 'Transfer' => transfer }
          # end
          p 'the stripe subscription is', subscription
           return "Premium Subscription is created"
        end
      else
        return "Couldn't find the subscription product be created please try again later."
      end
    rescue Stripe::StripeError => e
      return "Stripe error occured in subscription error: #{e.message}"
    end

  end

  def save_subscription(subscription)
    Subscription.create!(
      stripe_subscription_id: subscription.id,
      user_id: @buyer.id,
      listing_id: @listing.id,
      buying_offer_id: @buying_offer.id,
    )
  end

  def fetch_price_for_subscription(product)
    prices = Stripe::Price.list(product: product.id)
    price = prices.data.find { |p| p.metadata.listing_id == @listing.id }
    if price
      price
    else
      Stripe::Price.retrieve('price_1O9ucHCGUBvdXiZp3U6o1YGT')
    end
  end
end
