require 'rails_helper'

RSpec.describe AuthenticationController, type: :controller do
  describe '#authenticate_user' do
    context 'when the user is signed in' do
      before do
        allow(controller).to receive(:user_signed_in?).and_return(true)
      end

      it 'does not render unauthorized status' do
        expect(controller).not_to receive(:render).with(hash_including(status: :unauthorized))
        controller.send(:authenticate_user)
      end
    end

    context 'when the user is not signed in' do
      before do
        allow(controller).to receive(:user_signed_in?).and_return(false)
      end

      it 'renders unauthorized status' do
        expect(controller).to receive(:render).with(hash_including(status: :unauthorized))
        controller.send(:authenticate_user)
      end
    end
  end
end
