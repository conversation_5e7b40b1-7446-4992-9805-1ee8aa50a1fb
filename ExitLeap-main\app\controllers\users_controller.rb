class UsersController < ApplicationController
  before_action :authenticate_user!
  before_action :authorize_buyer, only: [:buyers]


  def update_address
    if current_user.update_or_create_address(address_params)
      updated_user = current_user.reload
      render json: { message: 'Address updated successfully', user: updated_user, address: updated_user.address }
    else
      render json: { error: 'Failed to update address', errors: current_user.errors.full_messages }, status: :unprocessable_entity
    end
  end

  def profile
    user = User.find(params[:id])
    render json: user
  end

  def update_profile
    user = User.find(params[:id])
    if user.update_email_or_name(profile_params)
      render json: { message: 'User updated successfully', user: user}
    else 
      render json: { error: 'Failed to update user', errors: user.errors.full_messages }, status: :unprocessable_entity
    end
  end


  def buyers
    buyers = User.buyer
    render json: buyers
  end

  def handle_buyer_card
    if current_user.buyer?
      if params[:card_token].blank?
        render json: { error: 'Stripe token is missing' }, status: :unprocessable_entity
        return
      end
    response, status = CardVerificationService.verify_card(current_user, params[:card_token])
    render json: response, status: status
    end
  end
def update_user
     if current_user.update_or_create_user(user_params)
       updated_user = current_user.reload
       render json: { message: 'Address updated successfully', user: updated_user}
     else
       render json: { error: 'Failed to update address', errors: current_user.errors.full_messages }, status: :unprocessable_entity
     end
   end

   def send_password_reset
    self.reset_password_token = generate_base64_token
    self.reset_password_sent_at = Time.zone.now
    save!
    UserMailer.password_reset(self).deliver_now
  end

  def password_token_valid?
    (self.reset_password_sent_at + 1.hour) > Time.zone.now
  end

  def reset_password(password)
    self.reset_password_token = nil
    self.password = password
    save!
  end

  def stripe_oauth_callback
    stripe_data = params[:stripe_data]

    if current_user.seller? && stripe_data
      seller_stripe = current_user.seller_stripe || current_user.build_seller_stripe
      seller_stripe.update(
        access_token: stripe_data[:access_token],
        token_type: stripe_data[:token_type],
        scope: stripe_data[:scope],
        livemode: stripe_data[:livemode],
        stripe_user_id: stripe_data[:stripe_user_id],
        stripe_publishable_key: stripe_data[:stripe_publishable_key],
        refresh_token: stripe_data[:refresh_token]
      )

      render json: { message: 'Stripe OAuth data saved successfully' }
    else
      render json: { error: 'Unauthorized or missing Stripe data' }, status: :unprocessable_entity
    end
  end

  def create_premium_subscription
    if current_user.buyer?
      # Check if the buyer already has a premium subscription
      if current_user.premium_subscription.present?
        render json: { status: 'Already subscribed to premium' }, status: 200
      else
        # Create a new premium subscription for the buyer
        stripe_service = FindOrCreateCustomer.call(current_user, fetch_payment_methods: true)
        # stripe_customer = stripe_service.call(fetch_payment_methods: true)
        current_user.update(stripe_customer_id: stripe_service.id)
        PremiumSubscriptionService.new(current_user).create_subscription


        premium_subscription = PremiumSubscription.create(user: current_user)

        render json: { message: 'Premium subscription created successfully', premium_subscription: premium_subscription }
      end
    else
      render json: { error: 'Unauthorized' }, status: :unauthorized
    end
  end

  private

  def address_params
    params.require(:user).permit(address_attributes: [:id ,:street_address, :address_line_2, :city, :state, :zipcode])
  end

  def profile_params
    params.require(:userProfile).permit(:id, :name, :email)
  end
  def generate_base64_token
    test = SecureRandom.urlsafe_base64
  end
  def user_params
    params.require(:user).permit(:name, :email)
  end

  def authorize_buyer
    return if current_user&.buyer?

    render json: { status: 'Unauthorized' }, status: :unauthorized
  end
end
