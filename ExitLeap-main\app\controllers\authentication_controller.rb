# app/controllers/authentication_controller.rb

class AuthenticationController < ApplicationController
  # before_action :authenticate_user!, only: [:refresh_token]

  def refresh_token
    user = User.find_by(refresh_token: params[:refresh_token])
    if user
      new_jwt_token = JwtEncoder.new(user).call
      render json: { status: { code: 200, message: 'Token refreshed successfully', data: { token: new_jwt_token } } }
    else
      render json: { status: 'Invalid refresh token' }, status: :unauthorized
    end
  end

  private

  def authenticate_user
    return if user_signed_in?

    render json: {
      status: 'Unauthorized'
    }, status: :unauthorized
  end
end
