<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schedule Management - Real or AI?</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="admin-header">
            <h1>Schedule Management</h1>
            <button id="logout-btn" class="vote-btn ai-btn">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>
        
        <div class="admin-nav">
            <a href="index.html">Dashboard</a>
            <a href="upload.html">Upload New Image</a>
            <a href="schedule.html" class="active">Schedule</a>
        </div>
        
        <div class="admin-container">
            <div class="schedule-header">
                <h2>Scheduled Images</h2>
                <div class="schedule-actions">
                    <button id="refresh-btn" class="vote-btn real-btn">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <a href="upload.html" class="vote-btn real-btn" style="text-decoration: none;">
                        <i class="fas fa-plus"></i> Add New
                    </a>
                </div>
            </div>
            
            <div class="schedule-filters">
                <div class="form-group">
                    <label for="filter-month">Filter by Month</label>
                    <select id="filter-month">
                        <option value="all">All Months</option>
                        <option value="05-2025" selected>May 2025</option>
                        <option value="06-2025">June 2025</option>
                        <option value="07-2025">July 2025</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="filter-status">Filter by Status</label>
                    <select id="filter-status">
                        <option value="all">All Status</option>
                        <option value="scheduled">Scheduled</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                    </select>
                </div>
            </div>
            
            <div class="schedule-table-container">
                <table class="admin-table schedule-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Image</th>
                            <th>Answer</th>
                            <th>Status</th>
                            <th>Votes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="schedule-table-body">
                        <tr class="status-active">
                            <td>May 1, 2025</td>
                            <td><img src="../images/placeholder.jpg" alt="Challenge image" class="table-image"></td>
                            <td>AI</td>
                            <td><span class="status-badge active">Active</span></td>
                            <td>127</td>
                            <td>
                                <button class="table-btn view-btn" data-date="2025-05-01"><i class="fas fa-eye"></i></button>
                            </td>
                        </tr>
                        <tr class="status-completed">
                            <td>April 30, 2025</td>
                            <td><img src="../images/placeholder.jpg" alt="Challenge image" class="table-image"></td>
                            <td>Real</td>
                            <td><span class="status-badge completed">Completed</span></td>
                            <td>1,250</td>
                            <td>
                                <button class="table-btn view-btn" data-date="2025-04-30"><i class="fas fa-eye"></i></button>
                            </td>
                        </tr>
                        <tr class="status-scheduled">
                            <td>May 2, 2025</td>
                            <td><img src="../images/placeholder.jpg" alt="Challenge image" class="table-image"></td>
                            <td>Real</td>
                            <td><span class="status-badge scheduled">Scheduled</span></td>
                            <td>0</td>
                            <td>
                                <button class="table-btn edit-btn" data-date="2025-05-02"><i class="fas fa-edit"></i></button>
                                <button class="table-btn delete-btn" data-date="2025-05-02"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                        <tr class="status-scheduled">
                            <td>May 3, 2025</td>
                            <td><img src="../images/placeholder.jpg" alt="Challenge image" class="table-image"></td>
                            <td>AI</td>
                            <td><span class="status-badge scheduled">Scheduled</span></td>
                            <td>0</td>
                            <td>
                                <button class="table-btn edit-btn" data-date="2025-05-03"><i class="fas fa-edit"></i></button>
                                <button class="table-btn delete-btn" data-date="2025-05-03"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div id="schedule-message" class="schedule-message"></div>
            
            <!-- Image Details Modal -->
            <div id="image-modal" class="modal">
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <h2>Image Details</h2>
                    
                    <div class="modal-body">
                        <div class="image-details-container">
                            <div class="image-details-preview">
                                <img id="modal-image" src="../images/placeholder.jpg" alt="Challenge image">
                            </div>
                            
                            <div class="image-details-info">
                                <p><strong>Date:</strong> <span id="modal-date">May 1, 2025</span></p>
                                <p><strong>Correct Answer:</strong> <span id="modal-answer">AI-Generated</span></p>
                                <p><strong>Status:</strong> <span id="modal-status">Active</span></p>
                                <p><strong>Total Votes:</strong> <span id="modal-votes">127</span></p>
                                <p><strong>Real Votes:</strong> <span id="modal-real-votes">52</span></p>
                                <p><strong>AI Votes:</strong> <span id="modal-ai-votes">75</span></p>
                                <p><strong>Description:</strong> <span id="modal-description">No description available.</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <footer>
            <p>&copy; 2025 Real or AI Challenge - Admin Panel</p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if admin is logged in
            const isLoggedIn = localStorage.getItem('adminLoggedIn') === 'true';
            
            if (!isLoggedIn) {
                // Redirect to login page if not logged in
                window.location.href = 'login.html';
                return;
            }
            
            // Logout button handler
            document.getElementById('logout-btn').addEventListener('click', function() {
                localStorage.removeItem('adminLoggedIn');
                window.location.href = 'login.html';
            });
            
            // Refresh button handler
            document.getElementById('refresh-btn').addEventListener('click', function() {
                // In a real app, this would fetch the latest data from the server
                const scheduleMessage = document.getElementById('schedule-message');
                scheduleMessage.textContent = 'Schedule refreshed successfully!';
                scheduleMessage.className = 'schedule-message success';
                
                // Clear message after 3 seconds
                setTimeout(() => {
                    scheduleMessage.textContent = '';
                    scheduleMessage.className = 'schedule-message';
                }, 3000);
            });
            
            // Filter handlers
            const filterMonth = document.getElementById('filter-month');
            const filterStatus = document.getElementById('filter-status');
            
            function applyFilters() {
                const monthFilter = filterMonth.value;
                const statusFilter = filterStatus.value;
                
                // In a real app, this would filter the data from the server
                console.log('Applying filters:', { month: monthFilter, status: statusFilter });
                
                // For demo purposes, we'll just log the filter values
                // In a real app, you would update the table based on the filtered data
            }
            
            filterMonth.addEventListener('change', applyFilters);
            filterStatus.addEventListener('change', applyFilters);
            
            // Modal functionality
            const modal = document.getElementById('image-modal');
            const closeModal = document.querySelector('.close-modal');
            
            // View button handlers
            const viewButtons = document.querySelectorAll('.view-btn');
            
            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const date = this.getAttribute('data-date');
                    
                    // In a real app, this would fetch the image details from the server
                    // For demo purposes, we'll use mock data
                    const imageDetails = {
                        date: 'May 1, 2025',
                        answer: 'AI-Generated',
                        status: 'Active',
                        totalVotes: 127,
                        realVotes: 52,
                        aiVotes: 75,
                        description: 'This is a test image for the Real or AI challenge.'
                    };
                    
                    // Update modal content
                    document.getElementById('modal-date').textContent = imageDetails.date;
                    document.getElementById('modal-answer').textContent = imageDetails.answer;
                    document.getElementById('modal-status').textContent = imageDetails.status;
                    document.getElementById('modal-votes').textContent = imageDetails.totalVotes;
                    document.getElementById('modal-real-votes').textContent = imageDetails.realVotes;
                    document.getElementById('modal-ai-votes').textContent = imageDetails.aiVotes;
                    document.getElementById('modal-description').textContent = imageDetails.description || 'No description available.';
                    
                    // Show modal
                    modal.style.display = 'block';
                });
            });
            
            // Close modal when clicking the close button
            closeModal.addEventListener('click', function() {
                modal.style.display = 'none';
            });
            
            // Close modal when clicking outside the modal content
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
            
            // Edit button handlers
            const editButtons = document.querySelectorAll('.edit-btn');
            
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const date = this.getAttribute('data-date');
                    
                    // In a real app, this would redirect to an edit page with the image details
                    alert(`In a real app, this would open an edit form for the challenge on ${date}`);
                });
            });
            
            // Delete button handlers
            const deleteButtons = document.querySelectorAll('.delete-btn');
            
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const date = this.getAttribute('data-date');
                    
                    if (confirm(`Are you sure you want to delete the challenge for ${date}?`)) {
                        // In a real app, this would send a delete request to the server
                        alert(`In a real app, this would delete the challenge for ${date}`);
                        
                        // For demo purposes, we'll just show a success message
                        const scheduleMessage = document.getElementById('schedule-message');
                        scheduleMessage.textContent = `Challenge for ${date} deleted successfully!`;
                        scheduleMessage.className = 'schedule-message success';
                        
                        // Clear message after 3 seconds
                        setTimeout(() => {
                            scheduleMessage.textContent = '';
                            scheduleMessage.className = 'schedule-message';
                        }, 3000);
                    }
                });
            });
        });
    </script>
    
    <style>
        .schedule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .schedule-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .schedule-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .schedule-filters .form-group {
            flex: 1;
            min-width: 200px;
        }
        
        .schedule-table-container {
            overflow-x: auto;
        }
        
        .schedule-table {
            min-width: 800px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-badge.active {
            background-color: rgba(52, 152, 219, 0.2);
            color: var(--primary-color);
        }
        
        .status-badge.scheduled {
            background-color: rgba(241, 196, 15, 0.2);
            color: #f39c12;
        }
        
        .status-badge.completed {
            background-color: rgba(46, 204, 113, 0.2);
            color: var(--secondary-color);
        }
        
        .schedule-message {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: var(--border-radius);
            text-align: center;
        }
        
        .schedule-message.success {
            background-color: rgba(46, 204, 113, 0.2);
            color: var(--secondary-color);
        }
        
        .schedule-message.error {
            background-color: rgba(231, 76, 60, 0.2);
            color: var(--danger-color);
        }
        
        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            overflow: auto;
        }
        
        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            width: 80%;
            max-width: 800px;
            position: relative;
        }
        
        .close-modal {
            position: absolute;
            top: 1rem;
            right: 1.5rem;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
        }
        
        .modal-body {
            margin-top: 1.5rem;
        }
        
        .image-details-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .image-details-preview img {
            width: 100%;
            max-height: 400px;
            object-fit: contain;
            border-radius: var(--border-radius);
        }
        
        .image-details-info p {
            margin-bottom: 0.5rem;
        }
        
        @media (min-width: 768px) {
            .image-details-container {
                flex-direction: row;
            }
            
            .image-details-preview {
                flex: 0 0 50%;
            }
            
            .image-details-info {
                flex: 1;
            }
        }
        
        .view-btn {
            color: var(--primary-color);
        }
    </style>
</body>
</html>
