class SubscriptionsController < ApplicationController
  before_action :authenticate_user!

  def seller_subscriptions
    if current_user.seller?
      seller_listings = current_user.listings
      @listing_subscriptions = Subscription.includes(:listing, :buying_offer).where(listing_id: seller_listings.pluck(:id))
      render json: @listing_subscriptions.as_json(
        include: {
          listing: { only: [:id, :title, :description, :photo, :created_at, :updated_at, :user_id, :ttm_revenue, :ttm_profit, :asking_price, :status, :industry, :location, :employees] },
          buying_offer: { only: [:id, :listing_id, :user_id, :offer_price, :state, :created_at, :updated_at, :phone_no, :message] }
        }
      )
    else
      render json: { error: 'Unauthorized' }, status: :unauthorized
    end
  end

  def listing_subscriptions
    if current_user.seller?
      listing = current_user.listings.find(params[:listing_id])
      listing_subscriptions = Subscription.where(listing_id: listing.id)
      render json: listing_subscriptions
    else
      render json: { error: 'Unauthorized' }, status: :unauthorized
    end
  end

  def buyer_subscriptions
    if current_user.buyer?
      buyer_subscriptions = Subscription.includes(:buying_offer, :listing).where(user_id: current_user.id)
      render json: buyer_subscriptions.as_json(
        include: {
          buying_offer: { only: [:id, :listing_id, :user_id, :offer_price, :state, :created_at, :updated_at] },
          listing: { only: [:id, :title, :description, :photo, :created_at, :updated_at, :user_id, :ttm_revenue, :ttm_profit, :asking_price, :status, :industry, :location, :employees] }
        }
      )
    else
      render json: { error: 'Unauthorized' }, status: :unauthorized
    end
  end

end
