// Example database utility for connecting to MongoDB
// This would be used by the API endpoints to interact with the database

const { MongoClient } = require('mongodb');

// In a real application, this would come from environment variables
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DB_NAME = process.env.DB_NAME || 'real-or-ai';

// Connection cache
let cachedClient = null;
let cachedDb = null;

async function connectToDatabase() {
  // If we already have a connection, use it
  if (cachedClient && cachedDb) {
    return { client: cachedClient, db: cachedDb };
  }

  // If no connection, create a new one
  const client = new MongoClient(MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  await client.connect();
  const db = client.db(DB_NAME);

  // Cache the connection
  cachedClient = client;
  cachedDb = db;

  return { client, db };
}

// Collection names
const COLLECTIONS = {
  CHALLENGES: 'challenges',
  VOTES: 'votes',
  SUBSCRIBERS: 'subscribers',
};

// Example database operations

// Get the current day's challenge
async function getCurrentChallenge() {
  const { db } = await connectToDatabase();
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  return db.collection(COLLECTIONS.CHALLENGES).findOne({
    date: { $gte: today, $lt: new Date(today.getTime() + 24 * 60 * 60 * 1000) },
  });
}

// Record a vote
async function recordVote(vote, userId) {
  const { db } = await connectToDatabase();
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  // Check if user already voted today
  if (userId) {
    const existingVote = await db.collection(COLLECTIONS.VOTES).findOne({
      userId,
      date: { $gte: today, $lt: new Date(today.getTime() + 24 * 60 * 60 * 1000) },
    });
    
    if (existingVote) {
      throw new Error('User already voted today');
    }
  }
  
  // Record the vote
  return db.collection(COLLECTIONS.VOTES).insertOne({
    vote,
    userId: userId || null,
    date: new Date(),
  });
}

// Add a subscriber
async function addSubscriber(email) {
  const { db } = await connectToDatabase();
  
  // Check if email already exists
  const existingSubscriber = await db.collection(COLLECTIONS.SUBSCRIBERS).findOne({ email });
  
  if (existingSubscriber) {
    return { success: true, message: 'Already subscribed' };
  }
  
  // Add new subscriber
  await db.collection(COLLECTIONS.SUBSCRIBERS).insertOne({
    email,
    subscriptionDate: new Date(),
    active: true,
  });
  
  return { success: true, message: 'Subscription successful' };
}

// Reveal yesterday's results
async function revealYesterdayResults() {
  const { db } = await connectToDatabase();
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  yesterday.setHours(0, 0, 0, 0);
  
  // Update the challenge to revealed
  await db.collection(COLLECTIONS.CHALLENGES).updateOne(
    {
      date: { $gte: yesterday, $lt: new Date(yesterday.getTime() + 24 * 60 * 60 * 1000) },
    },
    {
      $set: { revealed: true },
    }
  );
  
  // Get the challenge and votes for statistics
  const challenge = await db.collection(COLLECTIONS.CHALLENGES).findOne({
    date: { $gte: yesterday, $lt: new Date(yesterday.getTime() + 24 * 60 * 60 * 1000) },
  });
  
  const votes = await db.collection(COLLECTIONS.VOTES)
    .find({
      date: { $gte: yesterday, $lt: new Date(yesterday.getTime() + 24 * 60 * 60 * 1000) },
    })
    .toArray();
  
  // Calculate statistics
  const totalVotes = votes.length;
  const realVotes = votes.filter(v => v.vote === 'real').length;
  const aiVotes = votes.filter(v => v.vote === 'ai').length;
  
  return {
    challenge,
    statistics: {
      totalVotes,
      realVotes,
      aiVotes,
      correctVotes: challenge.correctAnswer === 'real' ? realVotes : aiVotes,
      correctPercentage: totalVotes > 0 
        ? Math.round((challenge.correctAnswer === 'real' ? realVotes : aiVotes) / totalVotes * 100) 
        : 0,
    },
  };
}

module.exports = {
  connectToDatabase,
  COLLECTIONS,
  getCurrentChallenge,
  recordVote,
  addSubscriber,
  revealYesterdayResults,
};
