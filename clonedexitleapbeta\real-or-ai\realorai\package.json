{"name": "real-or-ai", "version": "1.0.0", "description": "A daily web game where users vote on whether an image is real or AI-generated", "private": true, "scripts": {"start": "serve .", "dev": "serve .", "build": "echo 'No build step required for static site'", "vercel-build": "echo 'Build step for Vercel deployment'"}, "keywords": ["ai", "game", "voting", "images"], "author": "", "license": "MIT", "dependencies": {"aws-sdk": "^2.1400.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "mongodb": "^5.6.0", "@sendgrid/mail": "^7.7.0"}, "devDependencies": {"serve": "^14.0.0", "dotenv": "^16.3.1"}, "engines": {"node": ">=14.0.0"}}