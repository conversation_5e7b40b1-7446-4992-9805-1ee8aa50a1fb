class Listing < ApplicationRecord
  attr_accessor :document_data
  belongs_to :seller, class_name: 'User', foreign_key: 'user_id'
  has_many_attached :photos
  has_many :documents, dependent: :destroy
  has_many :fundraise_campaigns, dependent: :destroy
  has_many :buying_offers
  has_many :subscriptions
  has_many :competitors, class_name: 'Competitor', foreign_key: 'listing_id', dependent: :destroy
  accepts_nested_attributes_for :competitors, allow_destroy: true
  has_many :ownerships, class_name: 'Ownership', foreign_key: 'listing_id', dependent: :destroy
  accepts_nested_attributes_for :ownerships, allow_destroy: true
  belongs_to :address, optional: true
  accepts_nested_attributes_for :address
  has_one :growth_opportunity
  accepts_nested_attributes_for :growth_opportunity
  enum status:{
    available: 0,
    sold: 1
  }
  enum state: {
    step_1: "0",
    step_2: "1",
    step_3: "2",
    step_4: "3",
    step_5: "4",
    step_6: "5",
    step_7: "6",
    step_8: "7",
    complete: 8,
    document: 'document'
  }
  attribute :premium, :boolean, default: false

  def set_state_from_step(step)
    step_states_mapping = {
      'one' => 'step_1',
      'two' => 'step_2',
      'three' => 'step_3',
      'four' => 'step_4',
      'five' => 'step_5',
      'six' => 'step_6',
      'seven' => 'step_7',
      'eight' => 'complete'
    }

    step_states_mapping[step] || 'unknown_step'
  end

end
