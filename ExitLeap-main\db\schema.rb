# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2024_04_10_150546) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "addresses", force: :cascade do |t|
    t.string "street_address"
    t.string "address_line_2"
    t.string "city"
    t.string "state"
    t.string "zipcode"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["user_id"], name: "index_addresses_on_user_id"
  end

  create_table "buying_offers", force: :cascade do |t|
    t.bigint "listing_id", null: false
    t.bigint "user_id", null: false
    t.decimal "offer_price"
    t.integer "state"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "message"
    t.string "phone_no"
    t.index ["listing_id"], name: "index_buying_offers_on_listing_id"
    t.index ["user_id"], name: "index_buying_offers_on_user_id"
  end

  create_table "campaign_investors", force: :cascade do |t|
    t.bigint "fundraise_campaign_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["fundraise_campaign_id"], name: "index_campaign_investors_on_fundraise_campaign_id"
    t.index ["user_id"], name: "index_campaign_investors_on_user_id"
  end

  create_table "competitors", force: :cascade do |t|
    t.string "name"
    t.string "website"
    t.bigint "listing_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["listing_id"], name: "index_competitors_on_listing_id"
  end

  create_table "documents", force: :cascade do |t|
    t.bigint "listing_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "file_type"
    t.bigint "file_id"
    t.string "file"
    t.string "original_filename"
    t.integer "statement_type", default: 0
    t.index ["file_type", "file_id"], name: "index_documents_on_file"
    t.index ["listing_id"], name: "index_documents_on_listing_id"
  end

  create_table "fundraise_campaigns", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "listing_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "title"
    t.text "description"
    t.index ["listing_id"], name: "index_fundraise_campaigns_on_listing_id"
    t.index ["user_id"], name: "index_fundraise_campaigns_on_user_id"
  end

  create_table "growth_opportunities", force: :cascade do |t|
    t.text "description"
    t.text "opportunity"
    t.bigint "listing_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["listing_id"], name: "index_growth_opportunities_on_listing_id"
  end

  create_table "listings", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.text "photo"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.decimal "ttm_revenue"
    t.decimal "ttm_profit"
    t.decimal "asking_price"
    t.integer "status", default: 0
    t.string "industry"
    t.string "location"
    t.integer "employees"
    t.boolean "premium"
    t.string "headline"
    t.string "url"
    t.text "business_model"
    t.date "date_founded"
    t.boolean "draft", default: false
    t.string "role"
    t.string "name"
    t.string "state"
    t.bigint "address_id"
    t.text "asking_price_reason"
    t.index ["address_id"], name: "index_listings_on_address_id"
    t.index ["employees"], name: "index_listings_on_employees"
    t.index ["industry"], name: "index_listings_on_industry"
    t.index ["location"], name: "index_listings_on_location"
    t.index ["user_id"], name: "index_listings_on_user_id"
  end

  create_table "orders", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "listing_id", null: false
    t.integer "state", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["listing_id"], name: "index_orders_on_listing_id"
    t.index ["user_id"], name: "index_orders_on_user_id"
  end

  create_table "ownerships", force: :cascade do |t|
    t.string "firstname"
    t.string "middlename"
    t.string "lastname"
    t.string "email", null: false
    t.integer "phone"
    t.integer "ownership"
    t.bigint "listing_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["listing_id"], name: "index_ownerships_on_listing_id"
  end

  create_table "premium_subscriptions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_premium_subscriptions_on_user_id"
  end

  create_table "seller_stripes", force: :cascade do |t|
    t.bigint "user_id"
    t.string "access_token"
    t.string "token_type"
    t.string "scope"
    t.boolean "livemode"
    t.string "stripe_user_id"
    t.string "stripe_publishable_key"
    t.string "refresh_token"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_seller_stripes_on_user_id"
  end

  create_table "subscriptions", force: :cascade do |t|
    t.string "stripe_subscription_id"
    t.bigint "user_id", null: false
    t.bigint "listing_id", null: false
    t.bigint "buying_offer_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["buying_offer_id"], name: "index_subscriptions_on_buying_offer_id"
    t.index ["listing_id"], name: "index_subscriptions_on_listing_id"
    t.index ["user_id"], name: "index_subscriptions_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "name"
    t.string "password"
    t.string "profile_picture"
    t.integer "role"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "jti", null: false
    t.string "refresh_token"
    t.string "stripe_customer_id"
    t.string "stripe_customer_token"
    t.string "stripe_card_token"
    t.string "stripe_seller_id"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["jti"], name: "index_users_on_jti", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["stripe_seller_id"], name: "index_users_on_stripe_seller_id", unique: true
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "addresses", "users"
  add_foreign_key "buying_offers", "listings"
  add_foreign_key "buying_offers", "users"
  add_foreign_key "campaign_investors", "fundraise_campaigns"
  add_foreign_key "campaign_investors", "users"
  add_foreign_key "competitors", "listings"
  add_foreign_key "documents", "listings"
  add_foreign_key "fundraise_campaigns", "listings"
  add_foreign_key "fundraise_campaigns", "users"
  add_foreign_key "growth_opportunities", "listings"
  add_foreign_key "listings", "users"
  add_foreign_key "orders", "listings"
  add_foreign_key "orders", "users"
  add_foreign_key "ownerships", "listings"
  add_foreign_key "premium_subscriptions", "users"
  add_foreign_key "seller_stripes", "users"
  add_foreign_key "subscriptions", "buying_offers"
  add_foreign_key "subscriptions", "listings"
  add_foreign_key "subscriptions", "users"
end
