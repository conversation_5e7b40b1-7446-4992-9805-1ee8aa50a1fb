/* Base styles and variables */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --danger-color: #e74c3c;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --gray-color: #95a5a6;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--light-color);
    padding: 0;
    margin: 0;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

/* Typography */
h1, h2, h3 {
    margin-bottom: 1rem;
    line-height: 1.2;
}

h1 {
    font-size: 2rem;
    text-align: center;
    color: var(--primary-color);
}

.subtitle {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--gray-color);
}

a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Header */
header {
    margin-bottom: 2rem;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Main content */
main {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Image container */
.image-container {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    margin-bottom: 1rem;
}

#daily-image {
    width: 100%;
    height: auto;
    display: block;
}

.image-date {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border-top-left-radius: var(--border-radius);
}

/* Voting section */
.voting-container, .voted-container {
    background-color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
}

.question {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.voting-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.vote-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.real-btn {
    background-color: var(--primary-color);
    color: white;
}

.ai-btn {
    background-color: var(--secondary-color);
    color: white;
}

.vote-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.vote-btn:active {
    transform: translateY(0);
}

/* Subscription section */
.subscription-container {
    background-color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    text-align: center;
}

#subscription-form {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 400px;
    margin: 1rem auto;
}

input[type="email"] {
    padding: 0.8rem;
    border: 1px solid var(--gray-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

button[type="submit"] {
    padding: 0.8rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
}

button[type="submit"]:hover {
    background-color: #2980b9;
}

.subscription-message {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: var(--primary-color);
}

/* Footer */
footer {
    text-align: center;
    padding: 1rem 0;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    color: var(--gray-color);
    font-size: 0.9rem;
}

/* Utility classes */
.hidden {
    display: none;
}

/* Results page specific styles */
.results-container {
    background-color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
}

.result-answer {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 1rem 0;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
}

.answer-real {
    background-color: rgba(52, 152, 219, 0.2);
    color: var(--primary-color);
}

.answer-ai {
    background-color: rgba(46, 204, 113, 0.2);
    color: var(--secondary-color);
}

.voting-stats {
    display: flex;
    justify-content: space-around;
    margin: 1.5rem 0;
}

.stat-box {
    text-align: center;
    padding: 1rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    min-width: 120px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--gray-color);
}

/* Admin panel styles */
.admin-container {
    background-color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.admin-nav {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.admin-nav a {
    padding: 0.5rem 1rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.admin-nav a:hover, .admin-nav a.active {
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
}

.admin-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: bold;
}

.form-group input, .form-group select {
    padding: 0.8rem;
    border: 1px solid var(--gray-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.image-preview {
    width: 100%;
    max-width: 400px;
    height: auto;
    margin: 1rem 0;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Responsive design */
@media (min-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }
    
    .voting-buttons {
        gap: 2rem;
    }
    
    .vote-btn {
        padding: 1rem 2rem;
        font-size: 1.2rem;
    }
    
    #subscription-form {
        flex-direction: row;
    }
    
    button[type="submit"] {
        flex: 0 0 150px;
    }
}
