# frozen_string_literal: true

class Users::SessionsController < Devise::SessionsController
  # before_action :configure_sign_in_params, only: [:create]
  include RackSessionsFix
  respond_to :json

  # Override the create action for sign-in
  def create
    user = User.find_by(email: params[:user][:email])
    if user && user.valid_password?(params[:user][:password])
      sign_in(user)
      refresh_token = user.refresh_token
      jwt_payload = { user_id: user.id }
      jwt_token = JWTWrapper.encode(jwt_payload)

      render json: {
        user: {
          id: user.id,
          name: user.name,
          profile_picture: user.profile_picture,
          role: user.role
        },
        status: 'Logged in successfully',
        token: jwt_token,
        refresh_token: refresh_token
      }
    else
      render json: { status: 'Invalid email or password' }, status: :unauthorized
    end
  end

  # Override the destroy action for sign-out
  def destroy
    if current_user
      sign_out(current_user)
      render json: {
        status: 200,
        message: 'Logged out successfully.'
      }, status: :ok
    else
      render json: {
        status: 401,
        message: "Couldn't find an active session."
      }, status: :unauthorized
    end
  end

  # def respond_with(current_user, _opts = {})
  # end

  # def respond_to_on_destroy
  # end
end
