class User < ApplicationRecord
  include Devise::JWT::RevocationStrategies::J<PERSON>Matcher
  has_many :listings
  has_many :orders, class_name: 'Order', foreign_key: 'user_id'
  has_many :buying_offers, class_name: 'BuyingOffer', foreign_key: 'user_id'
  has_many :fundraise_campaigns, class_name: 'FundraiseCampaign', foreign_key: 'user_id', dependent: :destroy
  has_many :campaign_investors, class_name: 'CampaignInvestor', foreign_key: 'user_id', dependent: :destroy
  has_one :seller_stripe
  has_many :subscriptions
  has_one :premium_subscription
  has_one :address
  accepts_nested_attributes_for :address
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable, :recoverable, 

  # attr_encrypted :refresh_token, key: Rails.application.credentials.jwt.encryption_key
  devise :database_authenticatable, :registerable, :validatable, :jwt_authenticatable, jwt_revocation_strategy: self
  validates :email, uniqueness: true, presence: true

  #enum
  enum role: {
    seller: 0,
    buyer: 1
  }

  def update_or_create_address(address_params)
    if address_params.dig(:address_attributes, :id).present?
      # Update the existing address if ID is provided
      address = address_params.delete(:address_attributes)
      self.address.update(address)
    else
      # Create a new address if ID is not provided
      self.create_address(address_params[:address_attributes])
    end
  end

  def update_email_or_name(profile_params)
    self.name = profile_params[:name] if profile_params[:name].present?
    self.email = profile_params[:email] if profile_params[:email].present?
    self.save
  end
## Check to see if this is duplication
  def send_password_reset
    self.reset_password_token = generate_base64_token
    self.reset_password_sent_at = Time.zone.now
    save!
    UserMailer.password_reset(self).deliver_now
  end

  def password_token_valid?
    (self.reset_password_sent_at + 1.hour) > Time.zone.now
  end

  def reset_password(password)
    self.reset_password_token = nil
    self.encrypted_password = password
    save!
  end

  private

  def generate_base64_token
    test = SecureRandom.urlsafe_base64
  end
end
