# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>
DB_NAME=real-or-ai

# Image Storage (AWS S3)
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=real-or-ai-images

# Email Service (SendGrid)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDER_EMAIL=<EMAIL>
SENDER_NAME=Real or AI Challenge

# Admin Authentication
ADMIN_USERNAME=admin
ADMIN_PASSWORD_HASH=hashed_password_here
JWT_SECRET=your_jwt_secret_key

# Application Settings
NODE_ENV=production
TIMEZONE=America/New_York
REVEAL_HOUR=0  # Midnight in specified timezone
