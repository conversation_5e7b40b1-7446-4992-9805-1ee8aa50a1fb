// Example serverless function for handling email subscriptions
// This would be deployed as a serverless function on Vercel

module.exports = async (req, res) => {
  // Set CORS headers to allow requests from your frontend
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,POST');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
  );

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow POST requests for subscriptions
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Parse the request body
    const { email } = req.body;

    // Validate email
    if (!email || !validateEmail(email)) {
      return res.status(400).json({ error: 'Invalid email address' });
    }

    // In a real implementation, you would:
    // 1. Check if the email already exists in your database
    // 2. Add the email to your database or email service (e.g., SendGrid, Mailchimp)
    // 3. Potentially send a confirmation email

    // For this example, we'll just return a success response
    console.log(`New subscription: ${email}`);

    // Return a success response
    return res.status(200).json({
      success: true,
      message: 'Subscription successful',
      data: {
        email,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error processing subscription:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

// Simple email validation function
function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return re.test(String(email).toLowerCase());
}
