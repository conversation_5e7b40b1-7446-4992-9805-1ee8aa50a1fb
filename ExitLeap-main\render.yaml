databases:
  - name: postgres
    ipAllowList: []
    databaseName: aquire_production
    user: aquire

services:
  - type: web
    name: aquire
    env: ruby
    buildCommand: "./render-build.sh"
    startCommand: "bundle exec rails s"
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: postgres
          property: connectionString
      - key: RAILS_MASTER_KEY
        sync: false