class BuyingOffersController < ApplicationController
  before_action :authenticate_user!

  def index
    @listing = Listing.find(params[:listing_id])
    @buying_offers = @listing.buying_offers
    render json: @buying_offers
  end

  def create
    @listing = Listing.find(params[:listing_id])
    existing_buying_offer = BuyingOffer.find_by(listing: @listing, user: current_user)
    if existing_buying_offer
      update_existing_buying_offer(existing_buying_offer)
    else
      create_new_buying_offer
    end
  end

  def destroy
    @buying_offer = BuyingOffer.find(params[:id])
    @buying_offer.destroy
    render json: { status: 'Buyer offer deleted successfully.' }
  end

  def list_buying_offers
    if current_user.seller?
      @seller_listings = current_user.listings
      @buying_offers = BuyingOffer.where(listing_id: @seller_listings.pluck(:id))
    elsif current_user.buyer?
      @buying_offers = BuyingOffer.where(user_id: current_user.id)
    else
      render json: { status: 'Unauthorized' }, status: :unauthorized
      return
    end

    buying_offers_with_listings = @buying_offers.map do |buying_offer|
      {
        buying_offer: buying_offer,
        listing: buying_offer.listing
      }
    end

    render json: buying_offers_with_listings
  end

  private

  def update_existing_buying_offer(existing_buying_offer)
    if current_user.buyer? && @listing.available?
      existing_buying_offer.update(buying_offer_params)
      begin
        NotificationChannel.broadcast_to(
          @listing.user_id,
          message: 'Offer Updated by buyer',
          buying_offer: existing_buying_offer
        )
      rescue => e
        Rails.logger.error("Error sending notification: #{e.message}")
      end
      render json: existing_buying_offer
    else
      render json: { status: 'Unauthorized or Listing is not available' }, status: :unauthorized
    end
  end

  def create_new_buying_offer
    if current_user.buyer? && @listing.available?
      @buying_offer = @listing.buying_offers.new(buying_offer_params)
      @buying_offer.user_id = current_user.id
      @buying_offer.state = :pending
      if @buying_offer.save
        begin
          NotificationChannel.broadcast_to(
            "notification_channel_#{@buying_offer.user_id}",
            message: 'New offer received',
            buying_offer: @buying_offer
          )
        rescue => e
          Rails.logger.error("Error sending notification: #{e.message}")
        end
        render json: @buying_offer, status: :created
      else
        render json: @buying_offer.errors, status: :unprocessable_entity
      end
    else
      render json: { status: 'Unauthorized or Listing is not available' }, status: :unauthorized
    end
  end

  def buying_offer_params
    params.require(:buying_offer).permit(:offer_price, :message, :phone_no)
  end
end
