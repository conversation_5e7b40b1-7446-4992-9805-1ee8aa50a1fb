Rails.application.routes.draw do
  mount ForestLiana::Engine => '/forest'
  devise_for :users, path: '', path_names: {
      sign_in: 'login',
      sign_out: 'logout',
      registration: 'signup'
    },
    controllers: {
      sessions: 'users/sessions',
      registrations: 'users/registrations'
    }

    resources :users, only: [] do
      collection do
        get :stripe_setup
        post :create_premium_subscription
        post 'handle_buyer_card', to: 'users#handle_buyer_card'
      end
    end
    patch '/update_address', to: 'users#update_address'
    get 'authentication/refresh_token', to: 'authentication#refresh_token', as: 'refresh_token'
    get 'users/buyers', to: 'users#buyers', as: 'buyer_list'
    get 'users/profile/:id', to: 'users#profile', as: 'profile'
    post 'users/profile/:id', to: 'users#update_profile', as: 'update_profile'
    get '/stripe/oauth/callback', to: 'users#stripe_oauth_callback'
    patch '/update_user', to: 'users#update_user'
    post '/forgot_password', to: "users/passwords#forgot"
    post '/reset_password', to: "users/passwords#reset"
    resources :payments, only: [:create] do
      post :payment_success, on: :collection
    end

    resources :fundraise_campaigns, only: [:create, :index]

    resources :listings do
      resources :buying_offers
      member do
        get 'listing_potential_buyers'
        patch 'accept_buying_offer/:id', to: 'listings#accept_buying_offer', as: 'accept_buying_offer'
        patch 'reject_buying_offer/:id', to: 'listings#reject_buying_offer', as: 'reject_buying_offer'
        delete 'remove_document/:document_id', to: 'listings#remove_document', as: 'remove_document'
        post 'fetch_documents_by_statement_type'
      end
      collection do
        get 'filter_and_sort'
        get 'sort'
      end
    end

    get 'potential_buyers', to: 'listings#potential_buyers'
    get '/buying_offers', to: 'buying_offers#list_buying_offers'
    get '/listings/:listing_id/subscriptions', to: 'subscriptions#list_seller_subscriptions'

    resources :subscriptions, only: [] do
      collection do
        get 'seller_subscriptions'
        get 'listing_subscriptions/:listing_id', action: 'listing_subscriptions', as: :listing_subscriptions
        get 'buyer_subscriptions'
      end
    end

    mount ActionCable.server => "/cable"
end
