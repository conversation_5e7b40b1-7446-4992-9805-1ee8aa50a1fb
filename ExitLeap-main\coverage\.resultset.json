{"RSpec": {"coverage": {"/Users/<USER>/code/ExitLeap/config/environment.rb": {"lines": [null, 1, null, null, 1]}, "/Users/<USER>/code/ExitLeap/config/application.rb": {"lines": [1, null, 1, null, null, null, 1, null, 1, 1, null, 1, 1, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null]}, "/Users/<USER>/code/ExitLeap/config/boot.rb": {"lines": [1, null, 1, 1]}, "/Users/<USER>/code/ExitLeap/config/environments/test.rb": {"lines": [1, null, null, null, null, null, null, 1, null, null, null, 1, null, null, null, null, 1, null, null, 1, 1, null, null, null, null, 1, 1, 1, null, null, 1, null, null, 1, null, null, 1, null, 1, null, null, null, null, 1, null, null, 1, null, null, 1, null, null, 1, null, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/config/initializers/cors.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, 1, 1, null, 1, null, null, null, null, null, null, 1, 1, 1, null, null, null, null, null, 1, 1, 1, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/config/initializers/devise.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, 1, 1, 1, null, null, 1, null, null, 1, null, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, 1, null, null, null, null, null, null, null, null, null, null, 1, 1, 1, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, 1, null, null, null, null, null, null, null, 1, 1, 1, 1, 0, null, 1, 0, 0, 0, null, 0, null, 0, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/config/initializers/filter_parameter_logging.rb": {"lines": [null, null, null, null, null, 1, null, null]}, "/Users/<USER>/code/ExitLeap/config/initializers/forest_liana.rb": {"lines": [1, 1]}, "/Users/<USER>/code/ExitLeap/config/initializers/inflections.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/config/initializers/jwt.rb": {"lines": [1, null, 1, 1, 1, 1, 1, null, null]}, "/Users/<USER>/code/ExitLeap/lib/tasks/jwt_wrapper.rb": {"lines": [1, 1, 0, null, null, 1, 0, null, null]}, "/Users/<USER>/code/ExitLeap/config/initializers/stripe.rb": {"lines": [1, 0, 0, 0, 0, null, 0, null, null, 1, null, null, null, null, 1]}, "/Users/<USER>/code/ExitLeap/config/initializers/websocket_rails.rb": {"lines": [1, 1, null]}, "/Users/<USER>/code/ExitLeap/spec/factories/users.rb": {"lines": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, null, null]}, "/Users/<USER>/code/ExitLeap/app/channels/notification_channel.rb": {"lines": [1, 1, 0, null, null, 1, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/channels/application_cable/channel.rb": {"lines": [1, 1, null, null]}, "/Users/<USER>/code/ExitLeap/app/channels/application_cable/connection.rb": {"lines": [1, 1, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/application_controller.rb": {"lines": [1, 1, 1, null, 1, 0, 0, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/authentication_controller.rb": {"lines": [null, null, 1, null, null, 1, 0, 0, 0, 0, null, 0, null, null, null, 1, null, 1, 2, null, 1, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/buying_offers_controller.rb": {"lines": [1, 1, null, 1, 0, 0, 0, null, null, 1, 0, 0, 0, 0, null, 0, null, null, null, 1, 0, 0, 0, null, null, 1, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, null, 0, null, null, null, null, 0, null, null, 1, null, 1, 0, 0, null, 0, null, null, null, null, null, 0, null, 0, null, 0, null, null, null, 1, 0, 0, 0, 0, 0, null, 0, null, null, null, null, null, 0, null, 0, null, 0, null, null, 0, null, null, null, 1, 0, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/fundraise_campaigns_controller.rb": {"lines": [1, 1, null, 1, 0, 0, 0, null, 0, null, null, null, 1, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, null, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, null, 0, null, null, null, null, 0, 0, null, 0, 0, null, null, 0, null, null, null, 1, 1, 0, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/listings_controller.rb": {"lines": [null, 1, 1, 1, 1, null, 1, 0, null, null, 1, 0, 0, null, 0, 0, 0, null, 0, null, null, null, 0, null, null, 1, 0, null, 0, null, null, null, null, null, null, 0, 0, null, null, 1, 0, 0, 0, null, 0, 0, null, 0, null, null, 1, 0, 0, 0, null, 0, 0, null, 0, null, null, 1, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, null, null, null, null, null, null, 0, 0, null, 0, null, null, 0, null, null, null, 1, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, 0, null, 0, null, 0, null, null, null, null, null, null, 0, 0, null, 0, null, null, null, 1, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, null, null, 1, 0, 0, 0, 0, null, null, null, null, 0, null, null, 1, 0, null, null, null, null, 0, null, null, 1, 0, 0, 0, null, null, 1, 0, 0, null, null, null, 1, 0, 0, 0, 0, null, 0, 0, null, 0, null, null, null, 1, 0, 0, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, 1, 0, 0, 0, null, null, 1, 0, 0, 0, 0, null, 0, 0, 0, null, 0, null, null, 1, 0, 0, 0, null, 0, 0, null, 0, 0, null, 0, null, 0, null, null, null, null, null, null, null, 0, null, 0, null, null, 0, null, null, null, null, 1, null, 1, 0, 0, null, null, null, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, null, 0, 0, 0, 0, null, null, null, null, null, null, 1, 0, null, null, 1, 0, 0, 0, 0, null, null, 1, 0, null, null, 1, 0, 0, 0, 0, null, 0, null, null, null, 1, 0, 0, 0, 0, null, 0, null, null, null, 1, 0, 0, 0, 0, null, 0, null, null, null, 1, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, null, null, 1, 0, 0, 0, null, 0, 0, null, 0, null, null, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, 0, 0, 0, 0, null, 0, null, null, null, null, 1, 0, 0, 0, 0, 0, null, 0, null, null, null, null, 1, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, null, null, 1, 0, null, null, null, 1, 0, 0, 0, 0, null, null, null, 0, 0, 0, null, null, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, 0, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, null, null, null, 1, 0, 0, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/payments_controller.rb": {"lines": [1, 1, null, 1, 0, 0, null, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, null, null, null, null, null, 0, null, null, null, 0, 0, null, null, null, 1, 0, 0, 0, 0, 0, 0, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/subscriptions_controller.rb": {"lines": [1, 1, null, 1, 0, 0, 0, 0, null, null, null, null, null, null, 0, null, null, null, 1, 0, 0, 0, 0, null, 0, null, null, null, 1, 0, 0, 0, null, null, null, null, null, null, 0, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/users_controller.rb": {"lines": [1, 1, 1, null, null, 1, 0, 0, 0, null, 0, null, null, null, 1, 0, 0, null, null, 1, 0, 0, 0, null, 0, null, null, null, 1, 0, 0, null, null, 1, 0, 0, 0, 0, null, 0, 0, null, null, null, null, 1, 0, null, 0, 0, 0, null, null, null, null, null, null, null, null, null, 0, null, 0, null, null, null, 1, 0, null, 0, 0, null, null, 0, null, 0, 0, null, null, 0, null, 0, null, null, 0, null, null, null, 1, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, 0, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/users/confirmations_controller.rb": {"lines": [null, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/users/omniauth_callbacks_controller.rb": {"lines": [null, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/users/passwords_controller.rb": {"lines": [null, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/users/registrations_controller.rb": {"lines": [null, null, 1, null, null, 1, 1, null, null, null, null, null, null, 1, 0, null, 0, 0, 0, 0, null, null, 0, 0, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, 1, 0, 0, null, null, null, null, 0, 0, 0, null, null, null, null, null, null, null, 1, 0, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/concerns/rack_sessions_fix.rb": {"lines": [1, 1, 1, 1, 0, null, 1, null, 1, 2, 2, 2, 0, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/users/sessions_controller.rb": {"lines": [null, null, 1, null, 1, 1, null, null, 1, 0, 0, 0, 0, 0, 0, null, 0, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, null, null, 1, 0, 0, 0, null, null, null, null, 0, null, null, null, null, null, null, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/controllers/users/unlocks_controller.rb": {"lines": [null, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/jobs/application_job.rb": {"lines": [1, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/mailers/application_mailer.rb": {"lines": [1, 1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/mailers/send_grid_mailer.rb": {"lines": [1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/models/address.rb": {"lines": [1, 1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/models/application_record.rb": {"lines": [1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/models/buying_offer.rb": {"lines": [1, 1, 1, 1, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/models/campaign_investor.rb": {"lines": [1, 1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/models/competitor.rb": {"lines": [null, 1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/models/document.rb": {"lines": [1, 1, 1, null, 1, null, null, null, null, 1, 0, null, null, 1, 0, 0, 0, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/models/fundraise_campaign.rb": {"lines": [1, 1, 1, 1, null, null]}, "/Users/<USER>/code/ExitLeap/app/models/growth_opportunity.rb": {"lines": [1, 1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/models/jwt_encoder.rb": {"lines": [null, 1, 1, 0, null, null, 1, 0, 0, null, null]}, "/Users/<USER>/code/ExitLeap/app/models/listing.rb": {"lines": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, null, 1, null, 1, null, 0, null, null, null, null, null, null, null, null, null, 0, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/models/order.rb": {"lines": [1, 1, 1, null, 1, null, null, null, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/models/ownership.rb": {"lines": [1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/models/premium_subscription.rb": {"lines": [1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/models/seller_stripe.rb": {"lines": [1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/models/subscription.rb": {"lines": [1, 1, 1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/models/user.rb": {"lines": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, null, null, null, null, 1, 1, null, null, 1, null, null, null, null, 1, 3, null, 1, 1, null, null, 2, null, null, null, 1, 2, 2, 2, null, null]}, "/Users/<USER>/code/ExitLeap/app/serializers/user_serializer.rb": {"lines": [1, 1, 1, null]}, "/Users/<USER>/code/ExitLeap/app/services/admin_commission_service.rb": {"lines": [1, 1, 0, null, null, 1, 0, 0, 0, null, null]}, "/Users/<USER>/code/ExitLeap/app/services/card_verification_service.rb": {"lines": [1, 1, 0, null, null, 0, null, 0, null, 0, 0, 0, null, 0, null, null, 0, 0, 0, null, 0, 0, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/services/find_or_create_customer.rb": {"lines": [1, 1, null, 1, 0, null, null, 1, 0, 0, 0, null, 0, 0, null, null, null, null, null, null, null, null, null, 0, 0, 0, null, null, 0, null, null, 0, 0, null, 0, 0, null, 0, null, 0, 0, null, null, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, null, null, null, null, null, null, 0, 0, 0, null, 0, null, null, null, null, 0, null, 0, null, null, null, null, null, null, null, null, null, 0, 0, null, null, 0, null, null, 0, null, null, null, 0, null, null]}, "/Users/<USER>/code/ExitLeap/app/services/premium_subscription_service.rb": {"lines": [1, 1, 0, null, null, 1, 0, null, 0, 0, 0, 0, 0, null, 0, null, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, null, 0, null, 0, 0, null, null, null, null, 1, 0, null, null, null, null, null, null, null, 1, 0, 0, 0, 0, null, 0, null, null, null]}, "/Users/<USER>/code/ExitLeap/app/services/subscription_service.rb": {"lines": [1, 1, 0, 0, 0, 0, null, null, 1, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, null, 0, null, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, 0, 0, null, null, null, null, 1, 0, null, null, null, null, null, null, null, 1, 0, 0, 0, 0, null, 0, null, null, null]}, "/Users/<USER>/code/ExitLeap/config/routes.rb": {"lines": [1, 1, 1, null, null, null, null, null, null, null, null, null, 1, 1, 1, 1, 1, null, null, 1, 1, 1, 1, 1, 1, 1, 1, null, null, 1, null, 1, 1, 1, 1, 1, 1, 1, 1, null, 1, 1, 1, null, null, null, 1, 1, 1, null, 1, 1, 1, 1, 1, null, null, null, 1, null]}, "/Users/<USER>/code/ExitLeap/spec/models/user_spec.rb": {"lines": [1, null, 1, 1, 1, 1, 1, 1, null, 1, 1, 1, null, 1, 1, 1, 1, null, 1, 1, null, 1, 1, 1, 1, null, 1, 1, 1, 1, 1, null, null]}, "/Users/<USER>/code/ExitLeap/spec/requests/buying_offer_spec.rb": {"lines": [1, null, 1, 1, 1, null, null]}}, "timestamp": 1712759996}}