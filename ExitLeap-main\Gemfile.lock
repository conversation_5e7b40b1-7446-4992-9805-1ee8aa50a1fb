GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.0.8)
      actionpack (= 7.0.8)
      activesupport (= 7.0.8)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (7.0.8)
      actionpack (= 7.0.8)
      activejob (= 7.0.8)
      activerecord (= 7.0.8)
      activestorage (= 7.0.8)
      activesupport (= 7.0.8)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.0.8)
      actionpack (= 7.0.8)
      actionview (= 7.0.8)
      activejob (= 7.0.8)
      activesupport (= 7.0.8)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (7.0.8)
      actionview (= 7.0.8)
      activesupport (= 7.0.8)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (7.0.8)
      actionpack (= 7.0.8)
      activerecord (= 7.0.8)
      activestorage (= 7.0.8)
      activesupport (= 7.0.8)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.0.8)
      activesupport (= 7.0.8)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (7.0.8)
      activesupport (= 7.0.8)
      globalid (>= 0.3.6)
    activemodel (7.0.8)
      activesupport (= 7.0.8)
    activerecord (7.0.8)
      activemodel (= 7.0.8)
      activesupport (= 7.0.8)
    activestorage (7.0.8)
      actionpack (= 7.0.8)
      activejob (= 7.0.8)
      activerecord (= 7.0.8)
      activesupport (= 7.0.8)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (7.0.8)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    aes_key_wrap (1.1.0)
    arel-helpers (2.14.0)
      activerecord (>= 3.1.0, < 8)
    ast (2.4.2)
    attr_encrypted (4.0.0)
      encryptor (~> 3.0.0)
    attr_required (1.0.2)
    axiom-types (0.1.1)
      descendants_tracker (~> 0.0.4)
      ice_nine (~> 0.11.0)
      thread_safe (~> 0.3, >= 0.3.1)
    bcrypt (3.1.20)
    bigdecimal (3.1.7)
    bindata (2.4.15)
    bootsnap (1.17.1)
      msgpack (~> 1.2)
    builder (3.2.4)
    childprocess (5.0.0)
    climate_control (0.2.0)
    coercible (1.0.0)
      descendants_tracker (~> 0.0.1)
    concurrent-ruby (1.2.3)
    crass (1.0.6)
    daemons (1.4.1)
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.3.4)
    debug (1.9.1)
      irb (~> 1.10)
      reline (>= 0.3.8)
    deepsort (0.5.0)
    descendants_tracker (0.0.4)
      thread_safe (~> 0.3, >= 0.3.1)
    devise (4.9.3)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise-jwt (0.11.0)
      devise (~> 4.0)
      warden-jwt_auth (~> 0.8)
    diff-lcs (1.5.1)
    docile (1.4.0)
    dry-auto_inject (1.0.1)
      dry-core (~> 1.0)
      zeitwerk (~> 2.6)
    dry-configurable (1.1.0)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-core (1.0.1)
      concurrent-ruby (~> 1.0)
      zeitwerk (~> 2.6)
    dry-inflector (1.0.0)
    dry-initializer (3.1.1)
    dry-logic (1.5.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0, < 2)
      zeitwerk (~> 2.6)
    dry-schema (1.13.3)
      concurrent-ruby (~> 1.0)
      dry-configurable (~> 1.0, >= 1.0.1)
      dry-core (~> 1.0, < 2)
      dry-initializer (~> 3.0)
      dry-logic (>= 1.4, < 2)
      dry-types (>= 1.7, < 2)
      zeitwerk (~> 2.6)
    dry-types (1.7.2)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    em-synchrony (1.0.6)
      eventmachine (>= 1.0.0.beta.1)
    encryptor (3.0.0)
    erubi (1.12.0)
    eventmachine (1.2.7)
    factory_bot (6.4.6)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.3.1)
      i18n (>= 1.8.11, < 2)
    faye-websocket (0.11.3)
      eventmachine (>= 0.12.0)
      websocket-driver (>= 0.5.1)
    flay (2.13.2)
      erubi (~> 1.10)
      path_expander (~> 1.0)
      ruby_parser (~> 3.0)
      sexp_processor (~> 4.0)
    flog (4.8.0)
      path_expander (~> 1.0)
      ruby_parser (~> 3.1, > 3.1.0)
      sexp_processor (~> 4.8)
    forest_liana (8.1.0)
      arel-helpers
      bcrypt
      deepsort
      forestadmin-jsonapi-serializers (>= 0.14.0)
      groupdate (>= 5.0.0)
      httparty
      ipaddress
      json
      json-jwt (~> 1.15.0)
      jwt
      openid_connect (= 1.4.2)
      rack-cors
      rails (>= 4.0)
      useragent
    forestadmin-jsonapi-serializers (2.0.0.pre.beta.2)
      activesupport
    globalid (1.2.1)
      activesupport (>= 6.1)
    groupdate (6.4.0)
      activesupport (>= 6.1)
    hiredis (0.6.3)
    httparty (0.21.0)
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.1)
      concurrent-ruby (~> 1.0)
    ice_nine (0.11.2)
    io-console (0.7.2)
    ipaddress (0.8.3)
    irb (1.11.1)
      rdoc
      reline (>= 0.4.2)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json (2.7.1)
    json-jwt (1.15.3)
      activesupport (>= 4.2)
      aes_key_wrap
      bindata
      httpclient
    jsonapi-serializer (2.2.0)
      activesupport (>= 4.2)
    jwt (2.7.1)
    language_server-protocol (********)
    launchy (3.0.0)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    method_source (1.0.0)
    mime-types (3.5.2)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2023.1205)
    mimemagic (0.3.10)
      nokogiri (~> 1)
      rake
    mini_mime (1.1.5)
    minitest (5.21.2)
    msgpack (1.7.2)
    multi_xml (0.6.0)
    net-imap (*******)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (*******)
      net-protocol
    nio4r (2.7.0)
    nokogiri (1.16.3-x86_64-darwin)
      racc (~> 1.4)
    openid_connect (1.4.2)
      activemodel
      attr_required (>= 1.0.0)
      json-jwt (>= 1.15.0)
      net-smtp
      rack-oauth2 (~> 1.21)
      swd (~> 1.3)
      tzinfo
      validate_email
      validate_url
      webfinger (~> 1.2)
    orm_adapter (0.5.0)
    paperclip (6.1.0)
      activemodel (>= 4.2.0)
      activesupport (>= 4.2.0)
      mime-types
      mimemagic (~> 0.3.0)
      terrapin (~> 0.6.0)
    parallel (1.24.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    path_expander (1.1.1)
    pg (1.5.6)
    psych (5.1.2)
      stringio
    public_suffix (5.0.4)
    puma (5.6.8)
      nio4r (~> 2.0)
    racc (1.7.3)
    rack (2.2.8)
    rack-cors (2.0.1)
      rack (>= 2.0.0)
    rack-oauth2 (1.21.3)
      activesupport
      attr_required
      httpclient
      json-jwt (>= 1.11.0)
      rack (>= 2.1.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (7.0.8)
      actioncable (= 7.0.8)
      actionmailbox (= 7.0.8)
      actionmailer (= 7.0.8)
      actionpack (= 7.0.8)
      actiontext (= 7.0.8)
      actionview (= 7.0.8)
      activejob (= 7.0.8)
      activemodel (= 7.0.8)
      activerecord (= 7.0.8)
      activestorage (= 7.0.8)
      activesupport (= 7.0.8)
      bundler (>= 1.15.0)
      railties (= 7.0.8)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    railties (7.0.8)
      actionpack (= 7.0.8)
      activesupport (= 7.0.8)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.1.0)
    rdoc (6.6.2)
      psych (>= 4.0.0)
    redis (4.8.1)
    redis-objects (1.7.0)
      redis
    reek (6.3.0)
      dry-schema (~> 1.13.0)
      parser (~> 3.3.0)
      rainbow (>= 2.0, < 4.0)
      rexml (~> 3.1)
    regexp_parser (2.9.0)
    reline (0.4.2)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.2.6)
    rspec-core (3.13.0)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.1)
    rubocop (1.63.0)
      json (~> 2.3)
      language_server-protocol (>= 3.17.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.31.1, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 3.0)
    rubocop-ast (1.31.2)
      parser (>= *******)
    ruby-progressbar (1.13.0)
    ruby_parser (3.21.0)
      racc (~> 1.5)
      sexp_processor (~> 4.16)
    rubycritic (4.9.0)
      flay (~> 2.13)
      flog (~> 4.7)
      launchy (>= 2.5.2)
      parser (>= *******)
      rainbow (~> 3.1.1)
      reek (~> 6.0, < 7.0)
      rexml
      ruby_parser (~> 3.20)
      simplecov (>= 0.22.0)
      tty-which (~> 0.5.0)
      virtus (~> 2.0)
    rubyzip (2.3.2)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sexp_processor (4.17.1)
    shoulda-matchers (6.2.0)
      activesupport (>= 5.2.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    stringio (3.1.0)
    stripe (10.6.0)
    swd (1.3.0)
      activesupport (>= 3)
      attr_required (>= 0.0.5)
      httpclient (>= 2.4)
    terrapin (0.6.0)
      climate_control (>= 0.0.3, < 1.0)
    thin (1.8.2)
      daemons (~> 1.0, >= 1.0.9)
      eventmachine (~> 1.0, >= 1.0.4)
      rack (>= 1, < 3)
    thor (1.3.0)
    thread_safe (0.3.6)
    timeout (0.4.1)
    tty-which (0.5.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.5.0)
    useragent (0.16.10)
    validate_email (0.1.6)
      activemodel (>= 3.0)
      mail (>= 2.2.5)
    validate_url (1.0.15)
      activemodel (>= 3.0.0)
      public_suffix
    virtus (2.0.0)
      axiom-types (~> 0.1)
      coercible (~> 1.0)
      descendants_tracker (~> 0.0, >= 0.0.3)
    warden (1.2.9)
      rack (>= 2.0.9)
    warden-jwt_auth (0.8.0)
      dry-auto_inject (>= 0.8, < 2)
      dry-configurable (>= 0.13, < 2)
      jwt (~> 2.1)
      warden (~> 1.2)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webfinger (1.2.0)
      activesupport
      httpclient (>= 2.4)
    websocket (1.2.10)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    websocket-rails (0.7.0)
      em-synchrony
      faye-websocket
      hiredis
      rack
      rails
      redis
      redis-objects
      thin
    zeitwerk (2.6.12)

PLATFORMS
  x86_64-darwin-23

DEPENDENCIES
  attr_encrypted (~> 4.0.0)
  bootsnap
  database_cleaner-active_record
  debug
  devise (~> 4.7)
  devise-jwt (~> 0.7)
  eventmachine (~> 1.2.7)
  factory_bot_rails
  faker
  forest_liana (~> 8.0)
  hiredis (~> 0.6.3)
  jbuilder (~> 2.11.5)
  jsonapi-serializer (~> 2.2.0)
  launchy
  paperclip (~> 6.1.0)
  pg (~> 1.1)
  puma (~> 5.0)
  rack-cors
  rails (~> 7.0.8)
  redis (~> 4.0)
  rspec-rails
  rubocop
  rubycritic
  shoulda-matchers
  simplecov
  stripe (~> 10.6.0)
  thin (~> 1.8.2)
  tzinfo-data
  webdrivers
  websocket-rails (~> 0.7.0)

RUBY VERSION
   ruby 3.1.2p20

BUNDLED WITH
   2.5.5
