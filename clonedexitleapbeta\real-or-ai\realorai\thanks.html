<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thanks for Voting! - Real or AI?</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Real or AI?</h1>
            <p class="subtitle">Daily Challenge</p>
        </header>

        <main>
            <div class="voting-container">
                <i class="fas fa-check-circle" style="font-size: 4rem; color: var(--secondary-color); margin-bottom: 1rem;"></i>
                <h2>Thank You for Voting!</h2>
                <p>Your vote has been recorded.</p>
                <p>The answer will be revealed at midnight EST.</p>
                <p>Come back tomorrow for a new challenge!</p>
                
                <div class="subscription-container" style="margin-top: 2rem; background: none; box-shadow: none; padding: 0;">
                    <p>Want to receive daily results?</p>
                    <form id="subscription-form">
                        <input type="email" id="email" placeholder="Enter your email" required>
                        <button type="submit">Subscribe</button>
                    </form>
                    <p class="subscription-message" id="subscription-message"></p>
                </div>
                
                <div style="margin-top: 2rem;">
                    <a href="index.html" class="vote-btn real-btn" style="text-decoration: none; display: inline-block;">
                        <i class="fas fa-home"></i> Back to Home
                    </a>
                </div>
            </div>
        </main>

        <footer>
            <p>Previous day's results are available <a href="results.html">here</a>.</p>
            <p>&copy; 2025 Real or AI Challenge</p>
        </footer>
    </div>

    <script>
        // Simple subscription form handler
        document.addEventListener('DOMContentLoaded', function() {
            const subscriptionForm = document.getElementById('subscription-form');
            const subscriptionMessage = document.getElementById('subscription-message');
            
            subscriptionForm.addEventListener('submit', function(event) {
                event.preventDefault();
                
                const email = event.target.elements.email.value;
                
                // In a real app, this would send the email to a server
                console.log(`Subscription email: ${email}`);
                
                // Show success message
                subscriptionMessage.textContent = 'Thank you for subscribing! You will receive daily results.';
                subscriptionMessage.style.color = 'var(--secondary-color)';
                
                // Reset form
                event.target.reset();
                
                // Clear message after 5 seconds
                setTimeout(() => {
                    subscriptionMessage.textContent = '';
                }, 5000);
            });
        });
    </script>
</body>
</html>
