 # Real or AI? Daily Challenge

A web-based game where users vote on whether a daily image is real or AI-generated.

## Overview

"Real or AI?" is a daily web game that challenges users to determine if an image is real or AI-generated. Each day, a new image is presented, and users can vote on whether they think it's real or AI-generated. The correct answer is revealed at midnight EST, along with voting statistics.

## Features

- **Daily Challenge**: A new image is presented each day
- **Simple Voting**: Users can vote "Real" or "AI" with a single click
- **Results Page**: Previous day's results with voting statistics
- **Email Subscription**: Optional feature for users to receive daily results
- **Admin Panel**: Backend interface for uploading images, setting correct answers, and scheduling

## Project Structure

```
real-or-ai/
├── index.html           # Main voting page
├── thanks.html          # Thank you page after voting
├── results.html         # Previous day's results
├── css/
│   └── style.css        # Main stylesheet
├── js/
│   └── main.js          # Main JavaScript file
├── images/              # Image storage
│   └── placeholder.jpg  # Placeholder image
└── admin/               # Admin panel
    ├── index.html       # Admin dashboard
    ├── login.html       # Admin login
    ├── upload.html      # Image upload page
    └── schedule.html    # Schedule management
```

## Implementation Details

This is a static HTML/CSS/JavaScript implementation that demonstrates the UI and basic functionality. In a production environment, you would need to implement:

1. **Backend API**: To handle votes, image uploads, and user data
2. **Database**: To store images, votes, and user information
3. **Authentication**: Secure admin login
4. **Scheduled Tasks**: For revealing results at midnight EST
5. **Email Service**: For sending daily results to subscribers

## Future Development

To make this a fully functional application, consider implementing:

1. **Server-side Implementation**:
   - Node.js with Express
   - MongoDB or PostgreSQL database
   - AWS S3 or similar for image storage

2. **Deployment**:
   - Deploy to Vercel as specified
   - Set up CI/CD pipeline

3. **Additional Features**:
   - User accounts (optional)
   - Social sharing
   - Historical archives of past challenges
   - Leaderboards for users with the most correct guesses

## Admin Access

For the demo version:
- Username: admin
- Password: admin123

## Local Development

To run the application locally, simply open the `index.html` file in a web browser.

## Deployment

This project is configured for easy deployment to Vercel. For detailed deployment instructions, please refer to the [DEPLOYMENT.md](DEPLOYMENT.md) file.

Quick deployment steps:
1. Push your code to GitHub
2. Import the repository in Vercel
3. Deploy with default settings

The included `vercel.json` file ensures proper configuration for static site hosting.

## License

This project is licensed under the MIT License.
